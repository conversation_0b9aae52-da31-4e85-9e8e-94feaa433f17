
 
记录时间：2025-06-09 17:52:24,928 
线程ID:[4] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/9 17:52:24

 
记录时间：2025-06-09 18:07:46,065 
线程ID:[5] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/9 18:07:46

 
记录时间：2025-06-09 18:07:54,722 
线程ID:[5] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:08:00,902 
线程ID:[5] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>,<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>,
                           <PERSON><PERSON>, F.Schedule<PERSON>tTime, F.<PERSON>orm<PERSON>, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:08:59,628 
线程ID:[5] ExamChangeNotify 
 -查询时间范围: 2022-06-01 00:00:00 到 2022-07-01 00:00:00

 
记录时间：2025-06-09 18:10:38,415 
线程ID:[5] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/9 18:10:38

 
记录时间：2025-06-09 18:10:41,887 
线程ID:[5] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:10:42,471 
线程ID:[5] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:10:49,261 
线程ID:[5] ExamChangeNotify 
 -查询时间范围: 2022-06-01 00:00:00 到 2022-07-01 00:00:00
