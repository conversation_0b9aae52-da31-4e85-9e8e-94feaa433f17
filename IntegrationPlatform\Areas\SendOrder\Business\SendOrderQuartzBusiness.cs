using System;
using System.Linq;
using System.Threading.Tasks;
using IntegrationPlatform.Business;
using IntegrationPlatform.Common;
using IntegrationPlatform.QuartzJob;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Quartz;
using Quartz.Impl;
using Quartz.Spi;
using YJAppoint.Models;
using YJAppoint.Tool;

namespace IntegrationPlatform.Areas.Business
{
    public class SendOrderQuartzBusiness
    {
        //private readonly IServiceProvider _serviceProvider;
        //private readonly YJAppointDbContext _dbContext;
        public IConfiguration config { get; }
        public ISchedulerFactory schedulerFactory { get; }
        public IJobFactory jobFactory { get; }

        public SendOrderQuartzBusiness(  //IServiceProvider serviceProvider  //, YJAppointDbContext dbContext, 
          ISchedulerFactory _schedulerFactory, IJobFactory _iJobFactory, IConfiguration Configuration)
        {
            // _serviceProvider = serviceProvider;
            schedulerFactory = _schedulerFactory;
            jobFactory = _iJobFactory;
            config = Configuration;
        }

        public async void Start()
        {
            LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "启动消息分发服务");
            try
            {
                //StdSchedulerFactory factory = new StdSchedulerFactory();
                //IScheduler scheduler = await factory.GetScheduler();
                //scheduler.JobFactory = new JobFactory(_serviceProvider);
                IScheduler scheduler = await schedulerFactory.GetScheduler();
                scheduler.JobFactory = jobFactory;
                int DistributeTime = Convert.ToInt32(config["AppSettings:SendOrderJob"] + "");
                TriggerState state = await scheduler.GetTriggerState(new TriggerKey("SendOrderTrigger"));
                if (state == TriggerState.None) //状态为none则创建作业调度
                {
                    await scheduler.Start();
                    var job = JobBuilder.Create<SendOrderJob>()
                        .WithIdentity("SendOrderJob")
                        .Build();
                    var trigger = TriggerBuilder.Create()
                        .WithIdentity("SendOrderTrigger")
                        .StartNow()
                        .WithSimpleSchedule(x => x
                            .WithIntervalInSeconds(DistributeTime)
                            .RepeatForever())
                        .Build();
                    await scheduler.ScheduleJob(job, trigger);
                }
                else
                {
                    //不为none(5)则重启作业调度
                    await scheduler.ResumeTrigger(new TriggerKey("SendOrderTrigger"));
                }

            }
            catch (Exception e)
            {
                LogHelper.WriteErrorLog($"消息分发异常-{ LogNames.ErrorLog.ToString()}", e);
            }
        }

        public async Task<bool> IsStart()
        {
            try
            {
                //StdSchedulerFactory factory = new StdSchedulerFactory();
                //IScheduler scheduler = await factory.GetScheduler();
                //scheduler.JobFactory = new JobFactory(_serviceProvider);
                IScheduler scheduler = await schedulerFactory.GetScheduler();
                scheduler.JobFactory = jobFactory;
                TriggerState state = await scheduler.GetTriggerState(new TriggerKey("SendOrderTrigger"));
                if (state == TriggerState.Normal || state == TriggerState.Blocked)
                {
                    return true;
                }
            }
            catch (Exception e)
            {

                LogHelper.WriteErrorLog($"是否自动启动消息分发异常-{ LogNames.ErrorLog.ToString()}", e);
            }
            return false;
        }

        public async Task Pause()
        {
            try
            {
                //StdSchedulerFactory factory = new StdSchedulerFactory();
                //IScheduler scheduler = await factory.GetScheduler();
                //scheduler.JobFactory = new JobFactory(_serviceProvider);
                IScheduler scheduler = await schedulerFactory.GetScheduler();
                scheduler.JobFactory = jobFactory;
                await scheduler.PauseTrigger(new TriggerKey("SendOrderTrigger"));
            }
            catch (Exception e)
            {

                LogHelper.WriteErrorLog($"停止消息分发异常-{ LogNames.ErrorLog.ToString()}", e);
            }
        }

        public void IsDistribution()
        {
            LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "启动即开起");
            //var isDistribution = _dbContext.WebConfig.FirstOrDefault(w => w.WebKey == "IsDistribution");
            var isDistribution = config["AppSettings:SendOrderQuartzIsStart"] == null ? "" : config["AppSettings:SendOrderQuartzIsStart"].ToString();
            if (isDistribution == "true")
            {
                Start();
            }
        }
    }
}