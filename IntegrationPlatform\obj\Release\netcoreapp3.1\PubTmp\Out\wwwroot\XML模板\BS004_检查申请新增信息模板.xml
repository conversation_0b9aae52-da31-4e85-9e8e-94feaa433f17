<POOR_IN200901UV ITSVersion="XML_1.0" xmlns="urn:hl7-org:v3" 
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
xsi:schemaLocation="urn:hl7-org:v3 ../multicacheschemas/POOR_IN200901UV.xsd">
<!-- 消息ID -->
<id root="2.16.156.10011.2.5.1.1" extension="CheckApply_SerialNumber"/>
<!-- 消息创建时间 -->
<creationTime value="CheckApply_CreationTime"/>
<!-- 消息的服务标识-->
<interactionId root="2.16.156.10011.2.5.1.2" extension="POOR_IN200901UV"/>
<!--处理代码，标识此消息是否是产品、训练、调试系统的一部分。D：调试；P：产品；T：训练 -->
<processingCode code="P"/>
<!-- 消息处理模式: A(Archive); I(Initial load); R(Restore from archive); T(Current 
processing) -->
<processingModeCode/>
<!-- 消息应答: AL(Always); ER(Error/reject only); NE(Never) -->
<acceptAckCode code="AL"/>
<!-- 接受者 -->
<receiver typeCode="RCV">
<device classCode="DEV" determinerCode="INSTANCE">
<!-- 接受者ID -->
<id>
<item root="2.16.156.10011.2.5.1.3" extension="ESB"/>
</id>
</device>
</receiver>
<!-- 发送者 -->
<sender typeCode="SND">
<device classCode="DEV" determinerCode="INSTANCE">
<!-- 发送者ID -->
<id>
<item root="2.16.156.10011.2.5.1.3" extension="TZYT"/>
</id>
</device>
</sender>
<!-- 封装的消息内容 -->
<controlActProcess classCode="CACT" moodCode="EVN">
<subject typeCode="SUBJ">
<observationRequest classCode="OBS" moodCode="RQO">
<!-- 检查申请单编号 必须项已使用 -->
<id>
<item root="2.16.156.10011.1.24" extension="CheckApply_AID"/>
</id>
<code/>
<!-- 申请单详细内容 -->
<text value=""/>
<!-- 必须项未使用 -->
<statusCode/>
<!--检查申请有效日期时间 -->
<effectiveTime xsi:type="IVL_TS">
<low value=""/>
<high value=""/>
</effectiveTime>
<!--优先（紧急）度-->
<priorityCode code="">
<displayName value=""/>
</priorityCode>
<!--开单医生/送检医生 -->
<author typeCode="AUT">
<!-- 开单时间 -->
<time value="CheckApply_DocSchedeleDate"/>
<!--申请单开立者签名-->
<signatureText value="CheckApply_DocInputName"/>
<assignedEntity classCode="ASSIGNED">
<!--开单医生工号 -->
<id>
<item extension="CheckApply_DocInputNo" root="2.16.156.10011.1.4"/>
</id>
<!--开单医生姓名 -->
<assignedPerson determinerCode="INSTANCE" classCode="PSN">
<name xsi:type="BAG_EN">
<item>
<part value="CheckApply_DocInputName"/>
</item>
</name>
</assignedPerson>
<!-- 申请科室信息 -->
<representedOrganization determinerCode="INSTANCE" classCode="ORG">
<!--申请科室编码 必须项已使用 -->
<id>
<item extension="CheckApply_ApplyDepNo" root="2.16.156.10011.1.26"/>
</id>
<!--申请科室名称 -->
<name xsi:type="BAG_EN">
<item>
<part value="CheckApply_ApplyDep"/>
</item>
</name>
</representedOrganization>
</assignedEntity>
</author>
<!--审核者-->
<verifier typeCode="VRF">
<!--审核日期时间 -->
<time value=""/>
<assignedEntity classCode="ASSIGNED">
<!--审核者工号 -->
<id>
<item extension="" root="2.16.156.10011.1.4"/>
</id>
<assignedPerson determinerCode="INSTANCE" classCode="PSN">
<!--审核者姓名 -->
<name xsi:type="DSET_EN">
<item>
<part value=""/>
</item>
</name>
</assignedPerson>
</assignedEntity>
</verifier>
<!-- 多个检查项目循环component2 -->
<component2>
<observationRequest classCode="OBS" moodCode="RQO">
<id>
<!--医嘱ID-->
<item root="2.16.156.10011.1.28" extension="CheckApply_DocDetailedNo"/>
</id>
<!--检查项目编码 必须项已使用 -->
<code code="CheckApply_ProjectNo">
<!--检查项目名称 -->
<displayName value="CheckApply_ProjectName"/>
</code>
<!-- 必须项未使用 -->
<statusCode/>
<methodCode>
<!--检查方式编码 -->
<item code="" codeSystem="2.16.156.10011.2.3.2.47" codeSystemName="">
<!--检查方式名称 -->
<displayName value=""/>
</item>
<!--检查类型编码 -->
<item code="">
<!--检查类型名称 -->
<displayName value=""/>
</item>
</methodCode>
<!--检查部位编码 -->
<targetSiteCode>
<item code="">
<!--检查部位名称 -->
<displayName value=""/>
</item>
</targetSiteCode>
<!--执行科室 -->
<location typeCode="LOC">
<!-- 执行时间 -->
<time>
<any value=""/>
</time>
<serviceDeliveryLocation classCode="SDLOC">
<serviceProviderOrganization determinerCode="INSTANCE" 
classCode="ORG">
<!--执行科室编码 -->
<id>
<item extension="CheckApply_ExecutiveDepNo" root="2.16.156.10011.1.26"/>
</id>
<!-- 执行科室名称 -->
<name xsi:type="DSET_EN">
<item>
<part value="CheckApply_ExecutiveDepName"/>
</item>
</name>
</serviceProviderOrganization>
</serviceDeliveryLocation>
</location>
</observationRequest>
</component2>
<subjectOf6 contextConductionInd="false">
<!-- 必须项 未使用 default=false -->
<seperatableInd value="false"/>
<!--申请注意事项 -->
<annotation>
<text value=""/>
<statusCode code="completed"/>
<author>
<assignedEntity classCode="ASSIGNED"/>
</author>
</annotation>
</subjectOf6>
<!--就诊 -->
<componentOf1 contextConductionInd="false" xsi:nil="false" typeCode="COMP">
<!--就诊 -->
<encounter classCode="ENC" moodCode="EVN">
<id>
<!-- 就诊次数 -->
<item extension="CheckApply_DoctorTimes" root="2.16.156.10011.2.5.1.8"/>
<!-- 就诊流水号 -->
<item extension="CheckApply_RegisterID" root="2.16.156.10011.2.5.1.9"/>
</id>
<!--就诊类别编码-->
<code codeSystem="2.16.156.10011.2.3.1.271" codeSystemName="患者类型代码表" 
code="CheckApply_PatientSource">
<!-- 就诊类别名称 -->
<displayName value="CheckApply_PatiName"/>
</code>
<!--必须项未使用 -->
<statusCode/>
<!--病人 必须项未使用 -->
<subject typeCode="SBJ">
<patient classCode="PAT">
<id>
<!-- 域ID -->
<item root="2.16.156.10011.2.5.1.5" extension="01"/>
<!-- 患者ID -->
<item root="2.16.156.10011.2.5.1.4" extension="CheckApply_PatientID"/>
<!--门（急）诊号标识 -->
<item root="2.16.156.10011.1.10" extension="CheckApply_PatientFlag"/>
<!--住院号标识-->
<item root="2.16.156.10011.1.12" extension="CheckApply_HospFlag"/>
</id>
<!--个人信息 必须项已使用 -->
<patientPerson classCode="PSN">
<!-- 身份证号/医保卡号 -->
<id>
<!-- 身份证号 -->
<item extension="CheckApply_IDCard" 
root="2.16.156.10011.1.3"/>
<!-- 医保卡号 -->
<item extension="" root="2.16.156.10011.1.15"/>
</id>
<!--姓名 -->
<name xsi:type="DSET_EN">
<item>
<part value="CheckApply_PatientName"/>
</item>
</name>
<!-- 联系电话 -->
<telecom xsi:type="BAG_TEL">
<!-- 联系电话 -->
<item value="CheckApply_Tel"/>
</telecom>
<!--性别代码 -->
<administrativeGenderCode code="CheckApply_Sex" 
codeSystem="2.16.156.10011.*******"/>
<!--出生日期 -->
<birthTime value="CheckApply_BirthDay">
<!--年龄 -->
<originalText value="CheckApply_Age"/>
</birthTime>
<!--住址 -->
<addr xsi:type="BAG_AD">
<item use="H">
<part type="AL" value="CheckApply_Address"/>
</item>
</addr>
</patientPerson>
</patient>
</subject>
<!--住院位置-->
<location typeCode="LOC">
<time/>
<serviceDeliveryLocation classCode="SDLOC">
<location classCode="PLC" determinerCode="INSTANCE">
<!--DE01.00.026.00 病床号-->
<id>
<item extension="CheckApply_BedNumber"/>
</id>
<name xsi:type="BAG_EN">
<item use="IDE">
<part value="201"/>
</item>
</name>
<asLocatedEntityPartOf classCode="LOCE">
<location classCode="PLC" determinerCode="INSTANCE">
<!--DE01.00.019.00 病房号-->
<id>
<item extension=""/>
</id>
<name xsi:type="BAG_EN">
<item use="IDE">
<part value=""/>
</item>
</name>
</location>
</asLocatedEntityPartOf>
</location>
<serviceProviderOrganization classCode="ORG" 
determinerCode="INSTANCE">
<!--DE08.10.026.00 科室名称-->
<id>
<item extension="CheckApply_ExecutiveDepNo"/>
</id>
<name xsi:type="BAG_EN">
<item use="IDE">
<part value="CheckApply_ExecutiveDepName"/>
</item>
</name>
<asOrganizationPartOf classCode="PART">
<!-- DE08.10.054.00 病区名称 -->
<wholeOrganization classCode="ORG" 
determinerCode="INSTANCE">
<id>
<item extension="CheckApply_InpatientAreaNo"/>
</id>
<name xsi:type="BAG_EN">
<item use="IDE">
<part value="CheckApply_InpatientArea"/>
</item>
</name>
</wholeOrganization>
</asOrganizationPartOf>
</serviceProviderOrganization>
</serviceDeliveryLocation>
</location>
<!--诊断(检查申请原因) -->
<pertinentInformation1 typeCode="PERT" xsi:nil="false">
<observationDx classCode="OBS" moodCode="EVN">
<!--诊断类别编码 必须项已使用 -->
<code code="" codeSystem="2.16.156.10011.2.5.1.10">
<!--诊断类别名称 -->
<displayName value=""/>
</code>
<!-- 必须项未使用 -->
<statusCode code="active"/>
<!--诊断日期 -->
<effectiveTime>
<any value=""/>
</effectiveTime>
<!-- 疾病编码 必须项已使用 -->
<value code="" codeSystem="2.16.156.10011.2.3.3.11">
<!-- 疾病名称 -->
<displayName value=""/>
</value>
</observationDx>
</pertinentInformation1>
</encounter>
</componentOf1>
</observationRequest>
</subject>
</controlActProcess>
	<extra>
		<ClinicalDiagnosis displayName="临床诊断">CheckApply_ClinicalDiagnosis</ClinicalDiagnosis>
	    <ProjectPrice display="费用">CheckApply_ProjectPrice</ProjectPrice>
	    <Purpose display="检查目的">CheckApply_Purpose</Purpose>
	</extra>
</POOR_IN200901UV>