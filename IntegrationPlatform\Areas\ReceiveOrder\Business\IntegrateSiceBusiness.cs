using IntegrationPlatform.Areas.Models;
using IntegrationPlatform.Areas.ReceiveOrder.Models;
using IntegrationPlatform.Areas.SendOrder.Models;
using IntegrationPlatform.Common;
using IntegrationPlatform.Common.ApiHelper;
using IntegrationPlatform.Models;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.Intrinsics.X86;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Serialization;
using YJAppoint.Models;
using YJAppoint.ModelsObjects;
using YJAppoint.Tool;

namespace IntegrationPlatform.Areas.Business
{
    public class IntegrateSiceBusiness
    {
        private readonly YJAppointDbContext db;
        public Microsoft.Extensions.Configuration.IConfiguration config { get; }
        private static readonly object _locker = new object();
        private static readonly object _lock = new object(); // 定义静态锁对象

        private readonly IHostingEnvironment _hostingEnvironment;

        public IntegrateSiceBusiness(YJAppointDbContext applicationDbContext, Microsoft.Extensions.Configuration.IConfiguration Configuration, IHostingEnvironment hostingEnvironment)
        {
            db = applicationDbContext;
            config = Configuration;
            _hostingEnvironment = hostingEnvironment;
        }

        /// <summary>
        /// 接收医嘱信息
        /// </summary>
        /// <param name="dtoModel"></param>
        /// <returns></returns>
        public ResponseDto RegDocOrders(GetDocOrdersDto dtoModels)
        {
            //记录日志-接收的数据信息
            LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), JsonConvert.SerializeObject(dtoModels));
            Request dtoModel = dtoModels.Request;
            ResponseDto dto = new ResponseDto();
            dto.ResultCode = "-1";
            //获取集成的Url
            var integrationUrl = config["Url:IntegrationUrl"] + "";
            string ExamAddress = config["Url:ExamChangeNotify"];//申请单状态及内容变化接口(是我们自己的)
            try
            {
                ApplyInfo applyModel;
                PatientInfo patientModel = new PatientInfo();

                if (dtoModel == null)
                {
                    dto.ResultContent = "接收到的数据为空值";
                    return dto;
                }
                if (dtoModel.PatientInfo == null)
                {
                    dto.ResultContent = "接收到的患者数据为空值";
                    return dto;
                }
                if (dtoModel.AlyList.ApplyInformation == null || dtoModel.AlyList.ApplyInformation.Count == 0)
                {
                    dto.ResultContent = "接收到的申请单数据为空值";
                    return dto;
                }

                #region 患者信息
                try
                {
                    HandlePatientInfoAsync(dtoModel, patientModel, dto);
                }
                catch (Exception ex)
                {
                    // 捕获异常并记录日志，但不抛出异常
                    LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
                }
                #endregion

                #region 申请单信息表

                using (var tran = db.Database.BeginTransaction())
                {
                    var addDocDetailedNo = "";
                    try
                    {
                        //add by lwc 20250411
                        var allApplyInfos = dtoModel.AlyList.ApplyInformation;
                        var patientSource = dtoModel.PatientInfo.PatientSource;
                        bool anyHasBedside = patientSource?.ToUpper() == "I" &&
                             allApplyInfos.Any(x => x.ProjectName?.Contains("床旁") ?? false);

                        #region 处理门诊同一单号退费重收选取Transport最新的一笔数据逻辑addbylwc20250611
                        if (patientSource == "O")
                        {
                            // 按DocDetailedNo分组处理，每组内找到最新的数据
                            var groupedApplyInfos = allApplyInfos.GroupBy(x => x.DocDetailedNo).ToList();
                            var latestApplyInfos = new List<ApplyInfoDto>();
                            foreach (var group in groupedApplyInfos)
                            {
                                var applyInfosInGroup = group.ToList();

                                // 如果只有一条数据，直接添加
                                if (applyInfosInGroup.Count == 1)
                                {
                                    latestApplyInfos.Add(applyInfosInGroup.First());
                                }
                                else
                                {
                                    // 多条数据时，根据Transport字段规则选择最新的
                                    var latestApplyInfo = GetLatestApplyInfoByTransport(applyInfosInGroup, group.Key);
                                    if (latestApplyInfo != null)
                                    {
                                        latestApplyInfos.Add(latestApplyInfo);
                                    }
                                }
                            }
                            allApplyInfos = latestApplyInfos;
                        }
                        #endregion

                        foreach (var applyInfoItem in allApplyInfos)
                        {
                            ApplyInfo applyInfo;
                            string docDetailedNoStr = string.Empty;
                            //DONE或者查询匹配是为了预防存库的历史数据未替换 add by lwc 20250417
                            if (patientSource == "I")
                            {
                                applyInfo = db.ApplyInfo.Where(t => t.DocDetailedNo == applyInfoItem.DocDetailedNo || t.DocDetailedNo == applyInfoItem.DocGroupNo).SingleOrDefault();
                            }
                            else
                            {
                                applyInfo = db.ApplyInfo.Where(t => t.DocDetailedNo == applyInfoItem.DocDetailedNo).SingleOrDefault();
                            }
                            if (applyInfoItem.DocOperCode.Equals("NW"))
                            {
                                if (applyInfo == null)
                                {
                                    #region 不存在新增申请信息

                                    applyModel = new ApplyInfo();

                                    applyModel.PatientID = dtoModel.PatientInfo.PatientID;
                                    if (dtoModel.PatientInfo.PatientSource.ToUpper() == "I")
                                    {
                                        applyModel.PatientID = dtoModel.PatientInfo.BLH;
                                    }
                                    applyModel.PatientCode = dtoModel.PatientInfo.PatientID;
                                    applyModel.PatientSource = dtoModel.PatientInfo.PatientSource;
                                    applyModel.TreatmentNo = dtoModel.PatientInfo.TreatmentNo;

                                    applyModel.RegisterID = applyInfoItem.RegisterID;
                                    applyModel.DocOperCode = applyInfoItem.DocOperCode;
                                    applyModel.DocDetailedNo = applyInfoItem.DocDetailedNo;
                                    applyModel.DocGroupNo = applyInfoItem.DocGroupNo;
                                    //DONE 新增时住院医嘱号互换 add by lwc 20250417
                                    if (patientSource == "I")
                                    {
                                        applyModel.DocDetailedNo = applyInfoItem.DocGroupNo;
                                        applyModel.DocGroupNo = applyInfoItem.DocDetailedNo;
                                    }

                                    applyModel.AcessionNo = applyInfoItem.AcessionNo;
                                    applyModel.DocOrderCif = string.IsNullOrEmpty(applyInfoItem.DocOrderCif) ? 0 : Convert.ToInt32(applyInfoItem.DocOrderCif);
                                    applyModel.DocInputNo = applyInfoItem.DocMarkNo;
                                    applyModel.DocInputName = applyInfoItem.DocMarkName;
                                    applyModel.InputMark = string.IsNullOrEmpty(applyInfoItem.InputMark) ? 0 : Convert.ToInt32(applyInfoItem.InputMark);
                                    applyModel.DocScheduleNo = applyInfoItem.DocScheduleNo;
                                    applyModel.DocScheduleNaeme = applyInfoItem.DocScheduleNaeme;
                                    applyModel.DocTAakeEfDate = applyInfoItem.DocTAakeEfDate;
                                    applyModel.DocMarkNo = applyInfoItem.DocMarkNo;
                                    applyModel.DocMarkName = applyInfoItem.DocMarkName;
                                    applyModel.DocMarkDecNo = applyInfoItem.DocMarkDecNo;
                                    applyModel.DocMarkDecName = applyInfoItem.DocMarkDecName;
                                    applyModel.ClinicSign = string.IsNullOrEmpty(applyInfoItem.ClinicSign) ? 0 : Convert.ToInt32(applyInfoItem.ClinicSign);
                                    applyModel.DoctorTimes = applyInfoItem.Times;
                                    applyModel.DocSchedeleDate = applyInfoItem.DocSchedeleDate;

                                    applyModel.ExecutiveDepNo = applyInfoItem.ExecutiveDepNo?.Trim();
                                    applyModel.ExecutiveDepName = applyInfoItem.ExecutiveDepName;
                                    applyModel.ProjectState = "1";
                                    applyModel.ProjectNo = applyInfoItem.ProjectNo;

                                    //判断是否是床边项目，是在项目名称后加“（床边）”
                                    if (applyInfoItem.BEDSIDE == "1")
                                    {
                                        if (!applyInfoItem.ProjectName.Contains("（床边）"))
                                        {
                                            applyModel.ProjectName = applyInfoItem.ProjectName + "（床边）";
                                        }
                                        else
                                        {
                                            applyModel.ProjectName = applyInfoItem.ProjectName;
                                        }
                                    }
                                    else
                                    {
                                        applyModel.ProjectName = applyInfoItem.ProjectName;
                                    }

                                    applyModel.ProjectClass = applyInfoItem.ProjectClass;
                                    applyModel.ProjectCount = applyInfoItem.ProjectCount;
                                    applyModel.ProjectCom = applyInfoItem.ProjectCom;
                                    applyModel.ProjectPrice = applyInfoItem.ProjectPrice;
                                    applyModel.ChargeSign = string.IsNullOrEmpty(applyInfoItem.ChargeSign) || applyInfoItem.ChargeSign != "0" && applyInfoItem.ChargeSign != "2" ? 1 : Convert.ToInt32(applyInfoItem.ChargeSign);
                                    applyModel.PriceNumber = applyInfoItem.PriceNumber;
                                    applyModel.ChargeNumber = applyInfoItem.ChargeNumber;
                                    applyModel.InvoiceNub = applyInfoItem.InvoiceNub;
                                    applyModel.DocAdvice = applyInfoItem.DocAdvice;
                                    applyModel.AplicationForm = applyInfoItem.AplicationForm;
                                    applyModel.ApplyDep = applyInfoItem.ApplyDep;
                                    applyModel.ApplyDepNo = applyInfoItem.ApplyDepNo;
                                    applyModel.OtherBZ = applyInfoItem.OtherBZ;
                                    applyModel.HospitalAreaCode = applyInfoItem.HospitalAreaCode;
                                    applyModel.StudyNo = applyInfoItem.StudyNo;
                                    applyModel.CreateTime = DateTime.Now;
                                    //病例摘要
                                    applyModel.MedicalHistory = applyInfoItem?.MrSummary;
                                    //检查目的
                                    applyModel.AppendMessage = applyInfoItem.AppendMessage;
                                    applyModel.DistributeStatus = 0;
                                    if (dtoModel.PatientInfo.PatientSource?.ToUpper() == "I")
                                    {
                                        if (anyHasBedside)
                                        {
                                            // 如果有任意一个申请单包含"床旁"
                                            //applyModel.DistributeStatus = 1;//edit by lwc 20250417 会在调度的时候处理床旁
                                            // 如果当前申请单不包含"床旁"，则添加
                                            if (!applyInfoItem.OtherBZ?.Contains("床旁") ?? true)
                                            {
                                                applyModel.OtherBZ = string.IsNullOrEmpty(applyInfoItem.OtherBZ)
                                                    ? "床旁"
                                                    : applyInfoItem.OtherBZ + ";床旁";
                                            }
                                        }
                                    }
                                    #endregion 不存在新增申请信息

                                    db.ApplyInfo.Add(applyModel);
                                  
                                    db.SaveChanges();
                                   
                                    LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "新增申请单" + applyModel.DocDetailedNo);
                                    docDetailedNoStr = applyModel.DocDetailedNo;
                                }
                                else
                                {
                                    #region 存在修改申请信息处理 edit by lwc20250412
                                    if (applyInfo.DocOperCode == "CA")
                                    {
                                        applyInfo.DocOperCode = "NW";
                                        db.Entry(applyInfo).Property(x => x.DocOperCode).IsModified = true;
                                        applyInfo.UpdateTime = DateTime.Now.ToString();
                                        db.Entry(applyInfo).Property(x => x.UpdateTime).IsModified = true;
                                        db.SaveChanges();

                                        LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(),
                                            $"更新申请单{applyInfo.DocDetailedNo}的DocOperCode字段");
                                    }
                                    docDetailedNoStr = applyInfo.DocDetailedNo;
                                    #region 注释(废弃)
                                    //applyInfo.PatientID = dtoModel.PatientInfo.PatientID;
                                    //if (dtoModel.PatientInfo.PatientSource.ToUpper() == "I")
                                    //{
                                    //    applyInfo.PatientID = dtoModel.PatientInfo.BLH;
                                    //}
                                    //applyInfo.PatientCode = dtoModel.PatientInfo.PatientID;
                                    //applyInfo.PatientSource = dtoModel.PatientInfo.PatientSource;
                                    //applyInfo.TreatmentNo = dtoModel.PatientInfo.TreatmentNo;

                                    //applyInfo.RegisterID = applyInfoItem.RegisterID;
                                    //applyInfo.DocOperCode = applyInfoItem.DocOperCode;
                                    //applyInfo.DocDetailedNo = applyInfoItem.DocDetailedNo;
                                    //applyInfo.DocGroupNo = applyInfoItem.DocGroupNo;
                                    //applyInfo.AcessionNo = applyInfoItem.AcessionNo;
                                    //applyInfo.DocOrderCif = string.IsNullOrEmpty(applyInfoItem.DocOrderCif) ? 0 : Convert.ToInt32(applyInfoItem.DocOrderCif);
                                    //applyInfo.DocInputNo = applyInfoItem.DocInputNo;
                                    //applyInfo.DocInputName = applyInfoItem.DocInputName;
                                    //applyInfo.InputMark = string.IsNullOrEmpty(applyInfoItem.InputMark) ? 0 : Convert.ToInt32(applyInfoItem.InputMark);
                                    //applyInfo.DocScheduleNo = applyInfoItem.DocScheduleNo;
                                    //applyInfo.DocScheduleNaeme = applyInfoItem.DocScheduleNaeme;
                                    //applyInfo.DocTAakeEfDate = applyInfoItem.DocTAakeEfDate;
                                    //applyInfo.DocMarkNo = applyInfoItem.DocMarkNo;
                                    //applyInfo.DocMarkName = applyInfoItem.DocMarkName;
                                    //applyInfo.DocMarkDecNo = applyInfoItem.DocMarkDecNo;
                                    //applyInfo.DocMarkDecName = applyInfoItem.DocMarkDecName;
                                    //applyInfo.ClinicSign = string.IsNullOrEmpty(applyInfoItem.ClinicSign) ? 0 : Convert.ToInt32(applyInfoItem.ClinicSign);
                                    //applyInfo.DoctorTimes = applyInfoItem.Times;
                                    //applyInfo.DocSchedeleDate = applyInfoItem.DocSchedeleDate;

                                    //applyInfo.ExecutiveDepNo = applyInfoItem.ExecutiveDepNo?.Trim();
                                    //applyInfo.ExecutiveDepName = applyInfoItem.ExecutiveDepName;
                                    //applyInfo.ProjectState = "1";
                                    //applyInfo.ProjectNo = applyInfoItem.ProjectNo;

                                    ////判断是否是床边项目，是在项目名称后加“（床边）”
                                    //if (applyInfoItem.BEDSIDE == "1")
                                    //{
                                    //    if (!applyInfoItem.ProjectName.Contains("（床边）"))
                                    //    {
                                    //        applyInfo.ProjectName = applyInfoItem.ProjectName + "（床边）";
                                    //    }
                                    //    else
                                    //    {
                                    //        applyInfo.ProjectName = applyInfoItem.ProjectName;
                                    //    }
                                    //}
                                    //else
                                    //{
                                    //    applyInfo.ProjectName = applyInfoItem.ProjectName;
                                    //}

                                    //applyInfo.ProjectClass = applyInfoItem.ProjectClass;
                                    //applyInfo.ProjectCount = applyInfoItem.ProjectCount;
                                    //applyInfo.ProjectCom = applyInfoItem.ProjectCom;
                                    //applyInfo.ProjectPrice = applyInfoItem.ProjectPrice;
                                    //applyInfo.ChargeSign = string.IsNullOrEmpty(applyInfoItem.ChargeSign) || applyInfoItem.ChargeSign != "0" && applyInfoItem.ChargeSign != "2" ? 1 : Convert.ToInt32(applyInfoItem.ChargeSign);
                                    //applyInfo.PriceNumber = applyInfoItem.PriceNumber;
                                    //applyInfo.ChargeNumber = applyInfoItem.ChargeNumber;
                                    //applyInfo.InvoiceNub = applyInfoItem.InvoiceNub;
                                    //applyInfo.DocAdvice = applyInfoItem.DocAdvice;
                                    //applyInfo.AplicationForm = applyInfoItem.AplicationForm;
                                    //applyInfo.ApplyDep = applyInfoItem.ApplyDep;
                                    //applyInfo.ApplyDepNo = applyInfoItem.ApplyDepNo;
                                    //applyInfo.OtherBZ = applyInfoItem.OtherBZ;
                                    //applyInfo.HospitalAreaCode = applyInfoItem.HospitalAreaCode;
                                    //LogHelper.WriteInfo(LogNames.WebInfo.ToString(), applyInfoItem.DocDetailedNo + "申请单更新是否存在的影像号：" + applyInfo.StudyNo);
                                    //if (string.IsNullOrWhiteSpace(applyInfo.StudyNo))
                                    //{
                                    //    if (!string.IsNullOrWhiteSpace(applyInfo.MirrorStatusNote))
                                    //    {
                                    //        applyInfo.StudyNo = applyInfo.MirrorStatusNote;
                                    //    }
                                    //}
                                    //applyInfo.UpdateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                                    ////病例摘要
                                    //applyInfo.MedicalHistory = applyInfoItem?.MrSummary;
                                    ////检查目的
                                    //applyInfo.AppendMessage = applyInfoItem.AppendMessage;
                                    //applyInfo.DistributeStatus = 0;

                                    //db.ApplyInfo.Update(applyInfo);
                                    //db.SaveChanges();
                                    //LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "更新申请单" + applyInfo.DocDetailedNo);
                                    #endregion

                                    #endregion 存在修改申请信息
                                }

                                #region 推送申请数据到集成平台
                                try
                                {
                                    PushIntegrationRequest(integrationUrl, applyInfoItem, dtoModel.PatientInfo, docDetailedNoStr);
                                }
                                catch (Exception ex)
                                {
                                    LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "上游数据到集成平台推送异常" + ex.ToString());
                                }
                                #endregion
                            }
                            else if (applyInfoItem.DocOperCode.Equals("CA"))
                            {
                                if (applyInfo != null)
                                {
                                    #region 1.调用医技预约消息取消推送接口(废弃20250417)
                                    ////申请单NW的已分发的已预约(studyStatus=0)的去调推送预约消息接口
                                    ////combincode 有值且和docDtailNo 不同，说明为子单
                                    //if (applyInfo.DocOperCode == "NW" && applyInfo.DistributeStatus == 1)
                                    //{
                                    //    ApplyAfterDealInfo afterApplyInfo = null;
                                    //    string mainDocDetailNo = applyInfo.DocDetailedNo;
                                    //    if (!string.IsNullOrEmpty(applyInfo.CombinedCode) && !applyInfo.CombinedCode.Equals(applyInfo.DocDetailedNo))
                                    //    {
                                    //        //子单
                                    //        afterApplyInfo = db.ApplyAfterDealInfo.Where(d => d.AppFormNo.Equals(applyInfo.CombinedCode)).FirstOrDefault();
                                    //        mainDocDetailNo = applyInfo.CombinedCode;
                                    //    }
                                    //    else
                                    //    {
                                    //        afterApplyInfo = db.ApplyAfterDealInfo.Where(d => d.AppFormNo.Equals(applyInfo.DocDetailedNo)).FirstOrDefault();
                                    //    }

                                    //    if (afterApplyInfo?.StudyStatus == "0")
                                    //    {
                                    //        LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), $"【接收医嘱退费】推送取消预约开始{applyInfo.DocDetailedNo}");
                                    //        var SOMsg = db.SendOutMessage.Where(d => d.SendMegsType.Equals("预约") && d.DocDetailedNo == mainDocDetailNo).FirstOrDefault();
                                    //        if (SOMsg != null && !string.IsNullOrEmpty(SOMsg?.ReMegs))
                                    //        {
                                    //            LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "获取sendOutMsg预约数据：" + mainDocDetailNo);
                                    //            ////检查信息模型
                                    //            var bookedReMsg = XmlUtil.XmlDeserialize<BookedInfoReMsg>(SOMsg?.ReMegs, Encoding.UTF8);
                                    //            //获取推送消息模板内容
                                    //            var patientInfo = db.PatientInfo.FirstOrDefault(d => d.PatientID == applyInfo.PatientID);
                                    //            var message = GetCancelMessage(mainDocDetailNo, bookedReMsg, applyInfo, patientInfo, 1, db,applyInfoItem.DocDetailedNo);//edit by lwc 20250417
                                    //            LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "【接收医嘱退费】推送取消预约接口请求xml：" + message);
                                    //            string resu = CallByXML(integrationUrl, "CheckAppointStatusInfoUpdate", message);
                                    //            var resultCode = ResolutionXml(resu);
                                    //            if (resultCode == "AA")
                                    //            {
                                    //                LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), $"【接收医嘱退费】推送取消预约成功{applyInfo.DocDetailedNo}");
                                    //            }
                                    //        }
                                    //    }
                                    //}

                                    #endregion

                                    #region 2.处理CA申请单入库
                                    //医嘱操作码 NW：新增CA:取消
                                    applyInfo.DocOperCode = "CA";
                                    //向各个系统分发状态 0-未分发 1-已分发 2/3-分发异常
                                    applyInfo.DistributeStatus = 0;
                                    //项目状态 0-不处理 1-确认 2-拒绝 3-撤销 4-退费中
                                    //applyInfo.ProjectState = "4";
                                    //收费标志 0：不收费 1：收费 2：退费
                                    //applyInfo.ChargeSign = 2;
                                    //更新退费状态
                                    db.ApplyInfo.Update(applyInfo);
                                    db.SaveChanges();
                                    LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "" + applyInfo.DocDetailedNo + " 医嘱号退费成功");
                                    #endregion

                                    #region 3.申请单退费(废弃20250417)
                                    //#region 获取token
                                    //string clientName = config["AppSettings:ClientName"];
                                    //var clientInfo = db.ClientInfo.FirstOrDefault(a => a.ClientName == clientName);
                                    //var InDate = DateTime.Now.ToString("yyyyMMddHHmmss");
                                    //var myDigest = EncryptionCodingTool.HashSHACode(clientName + InDate + clientInfo.ClientCode);
                                    //var authStr = EncryptionCodingTool.Encrypt3DES(clientInfo.ClientCode + "$" + InDate + "$" + myDigest, clientInfo.PermitKey);
                                    //string token = clientName + "$" + authStr;
                                    //#endregion 获取token

                                    //ExamChangeNotify examChange = new ExamChangeNotify();
                                    //examChange.DocDetailedNo = applyInfo.DocDetailedNo;
                                    //examChange.PatientId = applyInfo.PatientID;
                                    //examChange.StudyStatus = "10";
                                    //string applyInfoJson = JsonConvert.SerializeObject(examChange);
                                    //string res = HttpExamChangeNotify(ExamAddress, applyInfoJson, token, LogNames.ReceiveInfo.ToString());
                                    #endregion
                                }
                                else
                                {
                                    dto.ResultContent += $"申请单号：{applyInfoItem.DocDetailedNo}，申请信息数据不存在或已删除。";
                                }
                            }
                        }
                        tran.Commit();
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), addDocDetailedNo + "添加/修改：" + ex.ToString());
                    }
                }

                #endregion 申请单信息表

                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "接收检查医嘱申请信息成功");
                dto.ResultCode = "0";
                dto.ResultContent = "成功";
            }
            catch (Exception ex)
            {
                dto.ResultContent = ex.Message;
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }

            return dto;
        }

        #region 退费推送取消预约消息逻辑

        /// <summary>
        /// 获取推送信息内容
        /// </summary>
        /// <param name="docDetailedNoStr"></param>
        /// <param name="bookedReMsg"></param>
        /// <param name="applyInfo"></param>
        /// <param name="patientInfo"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public string GetCancelMessage(string docDetailedNoStr, BookedInfoReMsg bookedReMsg, ApplyInfo applyInfo, PatientInfo patientInfo, int sendMesCount, YJAppointDbContext db,string InputDocDetailedNo)
        {
            var message = "";
            string scheduleTime = string.Empty;
            try
            {
                XmlDocument xd = new XmlDocument();
                string contentRootPath = _hostingEnvironment.ContentRootPath + "\\wwwroot" + "\\" + "XML模板" + "\\" + "BS004_检查预约状态新增信息模板.xml";
                xd.Load(contentRootPath);
                message = xd.InnerXml;//读取xml文件内容

                #region 赋值相关字段
                string msgTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                string patientSource = applyInfo.PatientSource;
                string patiName = GetSourceName(patientSource);
                string patiArea = patientInfo.InpatientArea?.Replace(patientInfo.Department, "");
                string patiAreaNo = patientInfo.InpatientAreaNo?.Replace(patientInfo.DepartmentNo, "");
                var afterDealInfo = applyInfo.ApplyAfterDealInfos.Where(d => d.AppFormNo == docDetailedNoStr).FirstOrDefault();
                var UserInfo = db.UserInfos.FromSql("SELECT * FROM UserInfos where RaleName = '" + bookedReMsg?.ScheduleUser + "'").FirstOrDefault();

                #endregion

                #region 更新xml内容
                if (sendMesCount > 0)
                {
                    message = message.Replace("PRSC_IN010101UV01", "PRSC_IN010201UV01");
                }

                message = message.Replace("BS004_SerialNumber", Guid.NewGuid().ToString());//消息流水号
                message = message.Replace("BS004_CreationTime", msgTime);
                message = message.Replace("BS004_Number", applyInfo.AID.ToString()); //预约单流水号edit by lwc 20250401
                message = message.Replace("BS004_PatientSource", patientSource);
                message = message.Replace("BS004_PatiName", patiName);
                message = message.Replace("BS004_ScheduleTime", "");//取消预约置空
                message = message.Replace("BS004_AreaID", "01");
                message = message.Replace("BS004_PatientID", applyInfo.PatientID);
                message = message.Replace("BS004_PatientFlag", patientInfo.BLH);//门诊号标识/病人卡号
                message = message.Replace("BS004_HospFlag", patientInfo.BLH);//住院号标识
                message = message.Replace("BS004_VisitNo", applyInfo.DoctorTimes);//就诊次数
                message = message.Replace("BS004_RegisterID", applyInfo.RegisterID);//就诊流水号
                message = message.Replace("BS004_SortID", bookedReMsg?.ScheduleNumber);
                message = message.Replace("BS004_PatientName", patientInfo.PatientName);
                message = message.Replace("BS004_ApplyDepNo", applyInfo.ApplyDepNo);
                message = message.Replace("BS004_ApplyDep", applyInfo.ApplyDep);
                message = message.Replace("BS004_EquipmentNo", bookedReMsg?.EquipmentNo);
                message = message.Replace("BS004_EquipmentName", bookedReMsg?.ExamRoomName);
                message = message.Replace("BS004_ScheduleUserNo", UserInfo?.WorkId);//预约员编码
                message = message.Replace("BS004_ScheduleUser", bookedReMsg?.ScheduleUser);
                message = message.Replace("BS004_ExecutiveDepNo", bookedReMsg?.ExecutiveDepNo);
                message = message.Replace("BS004_ExecutiveDepName", bookedReMsg?.ExecutiveDepName);

                //DONE add by lwc 20250417
                if (patientSource.ToUpper() == "I")
                {
                    if (applyInfo.DocDetailedNo != InputDocDetailedNo)
                    {
                        message = message.Replace("BS004_DocGroupNo", applyInfo.DocDetailedNo);
                        message = message.Replace("BS004_DocDetailedNo", applyInfo.DocGroupNo);
                    }
                    else
                    {
                        message = message.Replace("BS004_DocGroupNo", string.IsNullOrEmpty(applyInfo.DocGroupNo) ? docDetailedNoStr : applyInfo.DocGroupNo);//申请单编号edit by lwc 20250401
                        message = message.Replace("BS004_DocDetailedNo", applyInfo.DocDetailedNo);//医嘱号 edit by lwc 20250401
                    }
                }
                else
                {
                    //addbylwc20250424
                    message = message.Replace("BS004_DocGroupNo", string.IsNullOrEmpty(applyInfo.DocGroupNo) ? docDetailedNoStr : applyInfo.DocGroupNo);//申请单编号edit by lwc 20250401
                    message = message.Replace("BS004_DocDetailedNo", applyInfo.DocDetailedNo);//医嘱号 edit by lwc 20250401
                }
                message = message.Replace("BS004_PatientSex", bookedReMsg?.Sex);
                message = message.Replace("BS004_PatientAge", bookedReMsg?.Age);
                message = message.Replace("BS004_PatientTel", string.IsNullOrEmpty(patientInfo.Telephone) ? patientInfo.TelephoneNew : patientInfo.Telephone);
                message = message.Replace("BS004_Address", patientInfo?.Adress);
                message = message.Replace("BS004_IDCard", bookedReMsg?.SFZH);
                message = message.Replace("BS004_DocMarkName", applyInfo.DocMarkName);
                message = message.Replace("BS004_DocSchedeleDate", applyInfo.DocSchedeleDate);
                message = message.Replace("BS004_ClinicalDiagnosis", applyInfo.AplicationForm);
                message = message.Replace("BS004_ProjectNo", applyInfo.ProjectNo);
                message = message.Replace("BS004_ProjectName", applyInfo.ProjectName);
                message = message.Replace("BS004_ProjectPrice", applyInfo.ProjectPrice);
                message = message.Replace("BS004_InpatiAreaName", patiArea);
                message = message.Replace("BS004_InpatiAreaNo", patiAreaNo);
                message = message.Replace("BS004_OtherBZ", applyInfo.OtherBZ);
                #endregion
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), docDetailedNoStr + "推送信息内容模板获取失败：" + ex.ToString());
            }

            return message;
        }


        /// <summary>
        /// 获取消息推送结果
        /// </summary>
        /// <param name="Url"></param>
        /// <param name="action"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public string CallByXML(string Url, string action, string message)
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("p1", action);
            parameters.Add("p2", $"<![CDATA[{message}]]>");
            string xml = $@"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:urn=""urn:hl7-org:v3"">
                               <soapenv:Header/>
                               <soapenv:Body>
                                  <urn:HIPMessageServer>
                                     <urn:action>{parameters["p1"]}</urn:action>
                                     <urn:message>{parameters["p2"]}</urn:message>
                                  </urn:HIPMessageServer>
                               </soapenv:Body>
                            </soapenv:Envelope>";

            WebRequest webRequest = WebRequest.Create(Url);
            webRequest.ContentType = $"text/xml; charset=UTF-8";//application/soap+xml
            webRequest.Method = "POST";
            webRequest.Headers.Add("SOAPAction", "urn:HIPMessageServer");
            LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "集成平台的BS004【CallByXML】request：" + xml);
            using (Stream requestStream = webRequest.GetRequestStream())
            {
                byte[] paramBytes = Encoding.UTF8.GetBytes(xml);
                requestStream.Write(paramBytes, 0, paramBytes.Length);
            }

            //响应
            WebResponse webResponse = webRequest.GetResponse();
            string resp = JsonConvert.SerializeObject(webResponse);
            //LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "集成平台的BS004【CallByXML】response：" + resp);
            using (StreamReader myStreamReader = new StreamReader(webResponse.GetResponseStream(), Encoding.UTF8))
            {
                return myStreamReader.ReadToEnd();
            }
        }

        /// <summary>
        /// 解析SOAP格式返回的xml
        /// </summary>
        /// <param name="strxml"></param>
        /// <returns></returns>
        public string ResolutionXml(string strxml)
        {
            #region test
            //            strxml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
            //<soap:Envelope xmlns:soap=""http://www.w3.org/2003/05/soap-envelope"" xmlns:types=""urn:hl7-org:v3"">
            //	<soap:Body>
            //		<types:HIPMessageServerResponse>
            //			<types:payload>
            //				&lt;MCCI_IN000002UV01 xmlns=""urn:hl7-org:v3"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" ITSVersion=""XML_1.0"" xsi:schemaLocation=""urn:hl7-org:v3 ../multicacheschemas/MCCI_IN000002UV01.xsd""&gt;
            //				&lt;id extension=""2c321fcf-04d7-4f8e-beca-6a46254ccc77"" root=""2.16.156.10011.*******""/&gt;
            //				&lt;creationTime value=""20240809104748""/&gt;
            //				&lt;interactionId extension=""MCCI_IN000002UV01"" root=""2.16.156.10011.*******""/&gt;
            //				&lt;processingCode code=""P""/&gt;
            //				&lt;processingModeCode/&gt;
            //				&lt;acceptAckCode code=""AL""/&gt;
            //				&lt;receiver typeCode=""RCV""&gt;
            //				&lt;device classCode=""DEV"" determinerCode=""INSTANCE""&gt;
            //				&lt;id&gt;
            //				&lt;item extension=""@222"" root=""2.16.156.10011.*******""/&gt;
            //				&lt;/id&gt;
            //				&lt;/device&gt;
            //				&lt;/receiver&gt;
            //				&lt;sender typeCode=""SND""&gt;
            //				&lt;device classCode=""DEV"" determinerCode=""INSTANCE""&gt;
            //				&lt;id&gt;
            //				&lt;item extension=""ESB"" root=""2.16.156.10011.*******""/&gt;
            //				&lt;/id&gt;
            //				&lt;/device&gt;
            //				&lt;/sender&gt;
            //				&lt;acknowledgement typeCode=""AA""&gt;
            //				&lt;!--请求消息ID--&gt;
            //				&lt;targetMessage&gt;
            //				&lt;id extension=""@BS004"" root=""2.16.156.10011.*******""/&gt;
            //				&lt;/targetMessage&gt;
            //				&lt;acknowledgementDetail&gt;
            //				&lt;text value=""接收成功""/&gt;
            //				&lt;/acknowledgementDetail&gt;
            //				&lt;/acknowledgement&gt;
            //				&lt;/MCCI_IN000002UV01&gt;
            //			</types:payload>
            //		</types:HIPMessageServerResponse>
            //	</soap:Body>
            //</soap:Envelope>";
            #endregion
            string textCode = "";
            string textContes = "";
            try
            {
                // 使用 XmlSerializer 反序列化 XML 文档
                XmlSerializer xmlFormat = new XmlSerializer(typeof(Envelope));
                using (StringReader reader = new StringReader(strxml))
                {
                    Envelope envelope = (Envelope)xmlFormat.Deserialize(reader);
                    string payload = envelope.Body.HIPMessageServerResponse.Payload;
                    LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"SOAP Payload: {payload}");
                    McciIn000002UV01 payloadModel = ParsePayload(payload);
                    textCode = payloadModel.Acknowledgement.TypeCode.ToString();
                    textContes = payloadModel.Acknowledgement.AcknowledgementDetail.Text.Value;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), "解析SOAP格式返回的xml失败：" + ex.ToString());
            }
            return textCode;
        }

        // 解析 Payload 字符串
        private McciIn000002UV01 ParsePayload(string payload)
        {
            XmlSerializer serializer = new XmlSerializer(typeof(McciIn000002UV01));
            using (StringReader reader = new StringReader(payload))
            {
                return (McciIn000002UV01)serializer.Deserialize(reader);
            }
        }
        #endregion


        private void HandlePatientInfoAsync(Request dtoModel, PatientInfo patientModel, ResponseDto dto)
        {
            try
            {
                lock (_lock)
                {
                    if (string.IsNullOrEmpty(dtoModel.PatientInfo.PatientID) || string.IsNullOrEmpty(dtoModel.PatientInfo.SFZH))
                    {
                        dto.ResultContent = "患者ID、身份证号为必填";
                        return;
                    }
                    string patientID = dtoModel.PatientInfo.PatientID;
                    if (dtoModel.PatientInfo.PatientSource?.ToUpper() == "I")
                    {
                        patientID = dtoModel.PatientInfo.BLH;
                        if (string.IsNullOrEmpty(patientID))
                        {
                            dto.ResultContent = "住院患者ID必填";
                            return;
                        }
                    }
                    else
                    {
                        patientID = dtoModel.PatientInfo.PatientID;
                    }
                    //获取患者信息
                    var patientInfo = db.PatientInfo.FirstOrDefault(o => (o.PatientID == patientID));
                    if (patientInfo == null)
                    {
                        #region 患者不存在添加

                        patientModel.PatientID = patientID;
                        // 患者内码 即为 PID
                        patientModel.PatientCode = dtoModel.PatientInfo.PatientID;
                        patientModel.BLH = dtoModel.PatientInfo.PatientID;//update 20241114
                        //patientModel.BRKH = dtoModel.PatientInfo.BRKH;//暂时不写入数据，空置
                        patientModel.TreatmentNo = dtoModel.PatientInfo.TreatmentNo;
                        patientModel.SFZH = dtoModel.PatientInfo.SFZH;
                        patientModel.PatientNo = dtoModel.PatientInfo.PatientNo;
                        patientModel.PatientName = dtoModel.PatientInfo.PatientName;
                        patientModel.BirthDay = dtoModel.PatientInfo.BirthDay;
                        patientModel.Age = dtoModel.PatientInfo.Age;
                        patientModel.Sex = dtoModel.PatientInfo.Sex;
                        string patientSource = string.Empty;
                        patientModel.PatientSource = dtoModel.PatientInfo.PatientSource;
                        patientModel.ChargeType = dtoModel.PatientInfo.ChargeType;
                        patientModel.ChargeNo = dtoModel.PatientInfo.ChargeNo;
                        patientModel.HospitalStatus = dtoModel.PatientInfo.HospitalStatus;
                        patientModel.AttendingDoctor = dtoModel.PatientInfo.AttendingDoctor;
                        patientModel.AttendingDoctorNo = dtoModel.PatientInfo.AttendingDoctorNo;
                        patientModel.Department = dtoModel.PatientInfo.Department;
                        patientModel.DepartmentNo = dtoModel.PatientInfo.DepartmentNo;
                        patientModel.DiagnosisNo = dtoModel.PatientInfo.DiagnosisNo;
                        patientModel.Diagnosis = dtoModel.PatientInfo.Diagnosis;
                        patientModel.RyTime = dtoModel.PatientInfo.RyTime;
                        patientModel.CyTime = dtoModel.PatientInfo.CyTime;
                        patientModel.BedNumber = dtoModel.PatientInfo.BedNumber;
                        patientModel.InpatientArea = $"{dtoModel.PatientInfo.InpatientArea}{dtoModel.PatientInfo.Department}";
                        patientModel.InpatientAreaNo = $"{dtoModel.PatientInfo.InpatientAreaNo}{dtoModel.PatientInfo.DepartmentNo}";
                        patientModel.Adress = dtoModel.PatientInfo.Adress;
                        patientModel.Telephone = dtoModel.PatientInfo.Telephone;
                        patientModel.Marriage = dtoModel.PatientInfo.Marriage;
                        patientModel.Nation = dtoModel.PatientInfo.Nation;
                        patientModel.Nationality = dtoModel.PatientInfo.Nationality;
                        patientModel.OtherBZ = dtoModel.PatientInfo.OtherBZ;

                        patientModel.TelephoneNew = dtoModel.PatientInfo.Telephone;
                        patientModel.CreateTime = DateTime.Now;

                        #endregion 患者不存在添加

                        LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "新增患者" + patientModel.PatientID);
                        #region 再查询一次
                        var pinfo = db.PatientInfo.AsNoTracking().FirstOrDefault(o => o.PatientID == patientModel.PatientID);
                        #endregion
                        if (pinfo != null)
                        {
                            db.PatientInfo.Update(patientModel);
                            db.SaveChanges();
                        }
                        else
                        {
                            db.PatientInfo.Add(patientModel);
                            db.SaveChanges();
                        }
                    }
                    else
                    {
                        #region 患者存在;修改

                        patientInfo.PatientID = patientID;
                        // 患者内码 即为 PID
                        patientInfo.PatientCode = dtoModel.PatientInfo.PatientID;
                        patientInfo.BLH = dtoModel.PatientInfo.PatientID;
                        //patientInfo.BRKH = dtoModel.PatientInfo.BRKH;//暂时不写入数据，空置
                        patientInfo.TreatmentNo = dtoModel.PatientInfo.TreatmentNo;
                        patientInfo.SFZH = dtoModel.PatientInfo.SFZH;
                        patientInfo.PatientNo = dtoModel.PatientInfo.PatientNo;
                        patientInfo.PatientName = dtoModel.PatientInfo.PatientName;
                        patientInfo.BirthDay = dtoModel.PatientInfo.BirthDay;
                        patientInfo.Age = dtoModel.PatientInfo.Age;
                        patientInfo.Sex = dtoModel.PatientInfo.Sex;
                        patientInfo.PatientSource = dtoModel.PatientInfo.PatientSource;
                        patientInfo.ChargeType = dtoModel.PatientInfo.ChargeType;
                        patientInfo.ChargeNo = dtoModel.PatientInfo.ChargeNo;
                        patientInfo.HospitalStatus = dtoModel.PatientInfo.HospitalStatus;
                        patientInfo.AttendingDoctor = dtoModel.PatientInfo.AttendingDoctor;
                        patientInfo.AttendingDoctorNo = dtoModel.PatientInfo.AttendingDoctorNo;
                        patientInfo.Department = dtoModel.PatientInfo.Department;
                        patientInfo.DepartmentNo = dtoModel.PatientInfo.DepartmentNo;
                        patientInfo.DiagnosisNo = dtoModel.PatientInfo.DiagnosisNo;
                        patientInfo.Diagnosis = dtoModel.PatientInfo.Diagnosis;
                        patientInfo.RyTime = dtoModel.PatientInfo.RyTime;
                        patientInfo.CyTime = dtoModel.PatientInfo.CyTime;
                        patientInfo.BedNumber = dtoModel.PatientInfo.BedNumber;
                        patientInfo.InpatientArea = $"{dtoModel.PatientInfo.InpatientArea}{dtoModel.PatientInfo.Department}";
                        patientInfo.InpatientAreaNo = $"{dtoModel.PatientInfo.InpatientAreaNo}{dtoModel.PatientInfo.DepartmentNo}";
                        patientInfo.Adress = dtoModel.PatientInfo.Adress;
                        patientInfo.Telephone = dtoModel.PatientInfo.Telephone;
                        patientInfo.Marriage = dtoModel.PatientInfo.Marriage;
                        patientInfo.Nation = dtoModel.PatientInfo.Nation;
                        patientInfo.Nationality = dtoModel.PatientInfo.Nationality;
                        patientInfo.OtherBZ = dtoModel.PatientInfo.OtherBZ;

                        patientInfo.TelephoneNew = dtoModel.PatientInfo.Telephone;

                        patientInfo.UpdateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                        #endregion 患者存在;修改

                        LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "更新患者" + patientInfo.PatientID);
                        db.PatientInfo.Update(patientInfo);
                        db.SaveChanges();
                    }
                }
            }
            catch (DbUpdateException ex)
            {
                if (ex.InnerException is SqlException sqlEx && sqlEx.Number == 2627)
                {
                    // 处理唯一键约束冲突
                    LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "患者ID:" + dtoModel.PatientInfo.PatientID + "主键约束冲突，" + ex.ToString());
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "患者ID:" + dtoModel.PatientInfo.PatientID + "异常，" + ex.ToString());
            }
        }

        #region 推送申请到集成平台

        /// <summary>
        /// 推送申请到集成平台
        /// </summary>
        /// <param name="integrationUrl"></param>
        /// <param name="applyInfo"></param>
        /// <param name="patientInfo"></param>
        public void PushIntegrationRequest(string integrationUrl, ApplyInfoDto applyInfo, PatientInfoDto patientInfo, string docDetailedNoStr)
        {
            if (!int.TryParse(config["AppSettings:IntegrationRequest_TimeOut"], out int timeOut))
            {
                timeOut = 3; // 默认超时时间为3秒
            }
            var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeOut));
            try
            {
                //获取推送消息模板内容
                string message = GetApplyInfoMessage(applyInfo, patientInfo, docDetailedNoStr);
                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "推送CheckAppInfoAdd到集成平台的接口请求xml：" + message);
                string res = CallByXML(integrationUrl, "CheckAppInfoAdd", message, cts.Token).Result;
                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "推送CheckAppInfoAdd集成平台的接口返回xml：" + res);
            }
            catch (OperationCanceledException ex)
            {
                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "PushIntegrationRequest-方法执行超时：" + ex);
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }
        }

        /// <summary>
        /// 获取推送信息内容
        /// </summary>
        /// <param name="applyInfo"></param>
        /// <param name="patientInfo"></param>
        /// <returns></returns>
        public string GetApplyInfoMessage(ApplyInfoDto applyInfo, PatientInfoDto patientInfo,string docDetailedNoStr)
        {
            var message = "";
            string scheduleTime = string.Empty;
            string docDetailNo = docDetailedNoStr;
            try
            {
                XmlDocument xd = new XmlDocument();
                string contentRootPath = _hostingEnvironment.ContentRootPath + "\\wwwroot" + "\\" + "XML模板" + "\\" + "BS004_检查申请新增信息模板.xml";
                xd.Load(contentRootPath);
                message = xd.InnerXml;//读取xml文件内容

                #region 赋值相关字段
                string msgTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                string patientSource = patientInfo.PatientSource;
                string patiName = GetSourceName(patientSource);
                string patID = patientSource == "I" ? patientInfo.BLH : patientInfo.PatientID;
                #endregion

                #region 更新xml内容
                message = message.Replace("CheckApply_SerialNumber", Guid.NewGuid().ToString());
                message = message.Replace("CheckApply_AID", applyInfo.DocGroupNo);
                message = message.Replace("CheckApply_CreationTime", msgTime);
                message = message.Replace("CheckApply_DocSchedeleDate", applyInfo.DocSchedeleDate);
                message = message.Replace("CheckApply_DocInputName", applyInfo.DocMarkName);
                message = message.Replace("CheckApply_DocInputNo", applyInfo.DocMarkNo);
                message = message.Replace("CheckApply_ApplyDepNo", applyInfo.ApplyDepNo);
                message = message.Replace("CheckApply_ApplyDep", applyInfo.ApplyDep);
                message = message.Replace("CheckApply_DocDetailedNo", applyInfo.DocDetailedNo);
                message = message.Replace("CheckApply_ProjectNo", applyInfo.ProjectNo);
                message = message.Replace("CheckApply_ProjectName", applyInfo.ProjectName);
                message = message.Replace("CheckApply_ExecutiveDepNo", applyInfo.ExecutiveDepNo?.Trim());
                message = message.Replace("CheckApply_ExecutiveDepName", applyInfo.ExecutiveDepName);
                message = message.Replace("CheckApply_DoctorTimes", applyInfo.Times);
                message = message.Replace("CheckApply_RegisterID", applyInfo.RegisterID);
                message = message.Replace("CheckApply_PatientSource", patientSource);
                message = message.Replace("CheckApply_PatiName", patiName);
                message = message.Replace("CheckApply_PatientID", patientInfo.PatientID);
                message = message.Replace("CheckApply_PatientFlag", patientInfo.PatientID);
                message = message.Replace("CheckApply_HospFlag", patientInfo.BLH);
                message = message.Replace("CheckApply_IDCard", patientInfo.SFZH);
                message = message.Replace("CheckApply_PatientName", patientInfo.PatientName);
                message = message.Replace("CheckApply_Tel", patientInfo.Telephone);
                message = message.Replace("CheckApply_Sex", patientInfo.Sex == "男" ? "1" : "0");
                message = message.Replace("CheckApply_BirthDay", patientInfo.BirthDay);
                message = message.Replace("CheckApply_Age", patientInfo.Age);
                message = message.Replace("CheckApply_Address", patientInfo.Adress);
                message = message.Replace("CheckApply_BedNumber", patientInfo.BedNumber);
                message = message.Replace("CheckApply_InpatientAreaNo", patientInfo.InpatientAreaNo);
                message = message.Replace("CheckApply_InpatientArea", patientInfo.InpatientArea);
                message = message.Replace("CheckApply_ClinicalDiagnosis", applyInfo.AplicationForm);
                message = message.Replace("CheckApply_ProjectPrice", applyInfo.ProjectPrice);
                message = message.Replace("CheckApply_Purpose", applyInfo.AppendMessage);
                //message = message.Replace("CheckApply_ClinicSign", applyInfo.ClinicSign);//就诊类别
                #endregion
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), docDetailNo + "推送信息内容模板获取失败：" + ex.ToString());
            }

            return message;
        }

        private string GetSourceName(string source)
        {
            string result = string.Empty;
            switch (source)
            {
                case "I":
                    result = "住院";//住院
                    break;
                case "O":
                    result = "门诊";//门诊
                    break;
                case "E":
                    result = "急诊"; //急诊 现在划到门诊
                    break;
                case "P":
                    result = "体检"; //普通体检
                    break;
                default:
                    result = "";
                    break;
            };
            return result;
        }

        /// <summary>
        /// 获取消息推送结果
        /// </summary>
        /// <param name="Url"></param>
        /// <param name="action"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public async Task<string> CallByXML(string url, string action, string message, CancellationToken cancellationToken)
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("p1", action);
            parameters.Add("p2", $"<![CDATA[{message}]]>");
            string xml = $@"<?xml version=""1.0"" encoding=""utf-8""?><soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:urn=""urn:hl7-org:v3"">
                               <soapenv:Header/>
                               <soapenv:Body>
                                  <urn:HIPMessageServer>
                                     <urn:action>{parameters["p1"]}</urn:action>
                                     <urn:message>{parameters["p2"]}</urn:message>
                                  </urn:HIPMessageServer>
                               </soapenv:Body>
                            </soapenv:Envelope>";

            using (var httpClient = new HttpClient())
            {
                var request = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xml, Encoding.UTF8, "text/xml")
                };
                request.Headers.Add("SOAPAction", "urn:HIPMessageServer");
                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "检查申请新增soap xml【CallByXML】request：" + xml);
                HttpResponseMessage response = await httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                string resp = await response.Content.ReadAsStringAsync();

                return resp;
            }
        }

        #endregion

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public ResendResponseDto ResendDocOrders(ResendOrderDto dto)
        {
            ResendResponseDto res = new ResendResponseDto();
            try
            {
                var hisUrl = config["AppSettings:ResendOrders:ResendOrdersHisUrl"] + "";
                var range = config["AppSettings:ResendOrders:DateRange"] + "";
                string resendBeginDate = string.Empty;
                var currentDate = DateTime.Now;
                var resendEndDate = currentDate.AddDays(1).ToString("yyyy-MM-dd 00:00:00");
                if (!string.IsNullOrEmpty(range))
                {
                    resendBeginDate = currentDate.AddDays(Convert.ToInt32(range)).ToString("yyyy-MM-dd HH:mm:ss");
                }
                //string BLH = db.PatientInfo.Where(d => d.PatientID == dto.PatientId).Select(d => d.BLH).FirstOrDefault();
                //if (string.IsNullOrEmpty(BLH))
                //{
                //    BLH = dto.PatientId;
                //}
                var hisGetExam = new HisGetExamInfoDto
                {
                    SysCode = "APF",
                    BusinessType = "GetExamInfo",
                    Data = new List<Data>
                    {
                        new Data{ Examserial="",PatientId=dto.PatientId,BeginDate=resendBeginDate,EndDate=resendEndDate}
                    }
                };
                var jsonStr = JsonConvert.SerializeObject(hisGetExam);
                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "补发接收医嘱调用HisUrl【入参】" + jsonStr);
                var resultStr = RequestApi.HttpPost(hisUrl, jsonStr);
                //测试数据
                //string resultStr = "{\"code\":\"1\",\"message\":\"成功\",\"data\":[{\"Request\":{\"MesType\":\"0\",\"PatientInfo\":{\"PatientID\":\"001358512600\",\"PatientCode\":\"125122731\",\"BLH\":\"\",\"BRKH\":\"125122731\",\"SFZH\":\"130633199311096426\",\"PatientName\":\"郭丽影\",\"BirthDay\":\"19931109\",\"Age\":\"31\",\"Sex\":\"女\",\"PatientSource\":\"O\",\"ChargeType\":\"公费\",\"Department\":\"乳腺病科门诊\",\"DepartmentNo\":\"1120610\",\"BedNumber\":\"\",\"InpatientArea\":\"\",\"InpatientAreaNo\":\"\",\"Adress\":null,\"Telephone\":\"15733128894\",\"Marriage\":\"未婚\",\"Nation\":\"汉族\",\"Nationality\":\"中国\",\"OtherBZ\":\"\"},\"AlyList\":{\"ApplyInformation\":[{\"DocOperCode\":\"NW\",\"DocDetailedNo\":\"7186883\",\"DocGroupNo\":\"7186883\",\"DocSchedeleDate\":\"2024-12-23 09:23:42\",\"ExecutiveDepNo\":\"1120600\",\"ExecutiveDepName\":\"乳腺病科\",\"ProjectNo\":\"24015\",\"ProjectName\":\"局部热断层成像检查\",\"ProjectCount\":\"1\",\"ProjectPrice\":\"113.0000\",\"ApplyDep\":\"乳腺病科门诊\",\"ApplyDepNo\":\"1120610\",\"HospitalAreaCode\":\"01\",\"Transport\":\"\",\"AplicationForm\":null,\"AppendMessage\":\"协助诊断\",\"CallPriority\":\"0\",\"Times\":\"13\",\"DocMarkNo\":\"00079\",\"DocMarkName\":\"张晓军\"},{\"DocOperCode\":\"NW\",\"DocDetailedNo\":\"7186884\",\"DocGroupNo\":\"7186884\",\"DocSchedeleDate\":\"2024-12-23 09:23:42\",\"ExecutiveDepNo\":\"1120600\",\"ExecutiveDepName\":\"乳腺病科\",\"ProjectNo\":\"24008\",\"ProjectName\":\"乳腺常规超声检查\",\"ProjectCount\":\"1\",\"ProjectPrice\":\"114.0000\",\"ApplyDep\":\"乳腺病科门诊\",\"ApplyDepNo\":\"1120610\",\"HospitalAreaCode\":\"01\",\"Transport\":\"\",\"AplicationForm\":null,\"AppendMessage\":\"协助诊断\",\"CallPriority\":\"0\",\"Times\":\"13\",\"DocMarkNo\":\"00079\",\"DocMarkName\":\"张晓军\"}]}}}]}";
                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "补发接收医嘱调用HisUrl【出参】" + resultStr);
                var resultModel = JsonConvert.DeserializeObject<HisGetExamResponseDto>(resultStr);
                if (resultModel.Code == 1)
                {
                    string jsonData = Convert.ToString(resultModel.Data).TrimStart('[').TrimEnd(']');
                    var data = resultModel.Data;
                    if (jsonData != null)
                    {
                        GetDocOrdersDto dtoModels = JsonConvert.DeserializeObject<GetDocOrdersDto>(jsonData);
                        ResponseDto dtoModel1 = RegDocOrders(dtoModels);
                        if (dtoModel1.ResultCode == "0")
                        {
                            res.Code = 0;
                            res.Msg = "提取成功";
                        }
                        else
                        {
                            res.Code = -1;
                            res.Msg = dtoModel1.ResultContent;
                        }
                    }
                    else
                    {
                        res.Code = -1;
                        res.Msg = "提取失败";
                    }
                }
                else
                {
                    res.Code = -1;
                    res.Msg = resultModel.Message;
                }
            }
            catch (Exception ex)
            {
                res.Code = -1;
                res.Msg = "提取失败";
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }
            return res;
        }


        /// <summary>
        /// 接收电子申请单状态及内容变化
        /// </summary>
        /// <param name="dtoModel"></param>
        /// <returns></returns>
        public ResultData ExamChangeNotifyNew(string strxml, out string serialNumber)
        {
            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "接收申请单状态信息【ExamChangeNotify】接口入参：" + strxml);
            ExamChangeNotify dtoModel = new ExamChangeNotify();
            ResultData dto = new ResultData();
            dto.DataMsg = "-1";
            string operatName = string.Empty;
            string operatTime = string.Empty;
            string operatId = string.Empty;
            string normalFormat = string.Empty;
            string studyStatusCode = string.Empty;
            string studyStatusName = string.Empty;
            string groupNo = string.Empty;
            serialNumber = string.Empty;
            var integrationUrl = config["Url:IntegrationUrl"] + "";
            try
            {
                using (StringReader reader = new StringReader(strxml))
                {
                    XmlSerializer xmlFormat = new XmlSerializer(typeof(PoorIn200902UV));
                    PoorIn200902UV poorIn200902 = (PoorIn200902UV)xmlFormat.Deserialize(reader);
                    serialNumber = poorIn200902.Id.Extension;//消息流水号
                    var component2 = poorIn200902.ControlActProcess.Subject.PlacerGroup.Component2;
                    var patients = poorIn200902.ControlActProcess.Subject.PlacerGroup.ComponentOf1.Encounter.Subject.Patient.Id.Item;
                    var patientSource = poorIn200902.ControlActProcess.Subject.PlacerGroup.ComponentOf1.Encounter.Code.DisplayName.Value;
                    dtoModel.PatientId = patients.Where(d => d.Root == "2.16.156.10011.2.5.1.4").Select(d => d.Extension).FirstOrDefault();
                    groupNo = patients.Where(d => d.Root == "2.16.156.10011.1.12").Select(d => d.Extension).FirstOrDefault();
                    if (string.IsNullOrEmpty(dtoModel.PatientId))
                    {
                        dto.DataCode = "-1";
                        dto.DataMsg = "接收患者ID为空";
                        return dto;
                    }
                    foreach (var com in component2)
                    {
                        #region 从xml中找到各项值
                        if (com.ObservationRequest.Id.Item.Root == "2.16.156.10011.1.24")
                        {
                            if (!string.IsNullOrEmpty(groupNo))
                            {
                                groupNo = com.ObservationRequest.Id.Item.Extension;//申请单号
                                dtoModel.DocDetailedNo = db.ApplyInfo.Where(d => d.DocGroupNo == groupNo).Select(d => d.DocDetailedNo).FirstOrDefault();
                                //DONE add by lwc 20250417
                                if (patientSource == "住院" && string.IsNullOrEmpty(dtoModel.DocDetailedNo))
                                {
                                    dtoModel.DocDetailedNo = groupNo;
                                }
                            }
                            else
                            {
                                dtoModel.DocDetailedNo = com.ObservationRequest.Id.Item.Extension;//申请单号
                            }
                        }
                        if (com.ObservationRequest.Performer.AssignedEntity.Id.Item.Root == "2.16.156.10011.1.4")
                        {
                            operatId = com.ObservationRequest.Performer.AssignedEntity.Id.Item.Extension;
                        }
                        operatName = com.ObservationRequest.Performer.AssignedEntity.AssignedPerson.Name.Item.Part.Value;
                        operatTime = com.ObservationRequest.Performer.Time.Low.Value;
                        if (!string.IsNullOrEmpty(operatTime))
                        {
                            string format = "yyyyMMddHHmmss";
                            DateTime dateTime = DateTime.ParseExact(operatTime, format, CultureInfo.InvariantCulture);

                            // 输出为正常时间格式
                            normalFormat = dateTime.ToString("yyyy-MM-dd HH:mm:ss");
                        }
                        if (com.ObservationRequest.Component1.ProcessStep.Code.CodeSystem == "2.16.156.10011. *******2")
                        {
                            studyStatusCode = com.ObservationRequest.Component1.ProcessStep.Code.Codes;
                            studyStatusName = com.ObservationRequest.Component1.ProcessStep.Code.DisplayName.Value;
                        }
                        #endregion

                        dtoModel.StudyStatus = studyStatusCode;
                        dto = ExamChangeNotifyDetail(dtoModel, operatId, operatName, normalFormat);
                    }
                }
            }
            catch (Exception ex)
            {
                dto.DataMsg = ex.Message;
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }
            return dto;
        }


        /// <summary>
        /// 获取接收申请信息内容
        /// </summary>
        /// <param name="DocNo"></param>
        /// <param name="resultCode"></param>
        /// <param name="resultContent"></param>
        /// <returns></returns>
        public string GetExamStatusMessage(string serialNumber, string resultCode, string resultContent)
        {
            var message = "";
            try
            {
                XmlDocument xd = new XmlDocument();
                string contentRootPath = _hostingEnvironment.ContentRootPath + "\\wwwroot" + "\\" + "XML模板" + "\\" + "BS004_检查预约状态更新信息模板.xml";
                xd.Load(contentRootPath);
                message = xd.InnerXml;//读取xml文件内容

                #region 赋值相关字段
                string msgTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                #endregion

                #region 更新xml内容
                message = message.Replace("ExamStatus_SerialNumber", Guid.NewGuid().ToString());//消息流水号
                message = message.Replace("ExamStatus_CreationTime", msgTime);
                message = message.Replace("ExamStatus_ResultCode", resultCode);//返回code
                message = message.Replace("ExamStatus_SN", serialNumber);//消息流水号
                message = message.Replace("ExamStatus_ResultContent", resultContent);//返回结果

                #endregion
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), serialNumber + "-接收申请状态信息内容模板获取失败：" + ex.ToString());
            }
            return message;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="docDetailedNo"></param>
        /// <returns></returns>
        public ResultData PushExamChangeNotify(string integrationUrl, string docDetailedNo)
        {
            ResultData resultData = new ResultData();
            try
            {
                var afterApplyInfo = db.ApplyAfterDealInfo.Where(d => d.AppFormNo == docDetailedNo).FirstOrDefault();
                var applyInfo = db.ApplyInfo.Where(d => d.DocDetailedNo == docDetailedNo).FirstOrDefault();
                var patientInfo = db.PatientInfo.Where(d => afterApplyInfo != null && d.PatientID == afterApplyInfo.PatientID).FirstOrDefault();
                //获取推送消息模板内容
                string message = GetApplyInfoStatusMessage(applyInfo, patientInfo, afterApplyInfo);
                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "推送CheckStatusInfoUpdate到集成平台的接口请求xml：" + message);
                string res = CallStatusByXML(integrationUrl, "CheckStatusInfoUpdate", message).Result;
                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "推送CheckStatusInfoUpdate集成平台的接口返回xml：" + res);
                resultData.DataCode = "0";
                resultData.DataMsg = $"单号：{docDetailedNo}推送成功";
            }
            catch (Exception ex)
            {
                resultData.DataCode = "-1";
                resultData.DataMsg = $"单号：{docDetailedNo}推送失败";
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }

            return resultData;
        }

        /// <summary>
        /// 获取推送信息内容
        /// </summary>
        /// <param name="applyInfo"></param>
        /// <param name="patientInfo"></param>
        /// <returns></returns>
        public string GetApplyInfoStatusMessage(ApplyInfo applyInfo, PatientInfo patientInfo, ApplyAfterDealInfo applyAfter)
        {
            var message = "";
            string docDetailNo = applyInfo.DocDetailedNo;
            try
            {
                XmlDocument xd = new XmlDocument();
                string contentRootPath = _hostingEnvironment.ContentRootPath + "\\wwwroot" + "\\" + "XML模板" + "\\" + "检查状态更新服务.xml";
                xd.Load(contentRootPath);
                message = xd.InnerXml;//读取xml文件内容

                #region 赋值相关字段
                string msgTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                string patientSource = patientInfo.PatientSource;
                string patiName = GetSourceName(patientSource);
                #endregion

                #region 更新xml内容
                message = message.Replace("ExamStatus_SerialNumber", Guid.NewGuid().ToString());
                message = message.Replace("ExamStatus_CreationTime", msgTime);
                message = message.Replace("ExamStatus_DocDetailNo", applyInfo.DocDetailedNo);
                message = message.Replace("ExamStatus_OperatorId", applyAfter.StudyUserId);
                message = message.Replace("ExamStatus_Operator", applyAfter.StudyUser);
                message = message.Replace("ExamStatus_ExecutiveDepNo", applyInfo.ExecutiveDepNo);
                message = message.Replace("ExamStatus_ExecutiveDepName", applyInfo.ExecutiveDepName);
                message = message.Replace("ExamStatus_DoctorTimes", applyInfo.DoctorTimes);
                message = message.Replace("ExamStatus_RegisterID", applyInfo.RegisterID);
                message = message.Replace("ExamStatus_PatientSource", patientSource);
                message = message.Replace("ExamStatus_PatiName", patiName);
                #endregion
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), docDetailNo + "推送检查状态更新信息内容模板获取失败：" + ex.ToString());
            }

            return message;
        }

        /// <summary>
        /// 获取消息推送结果
        /// </summary>
        /// <param name="Url"></param>
        /// <param name="action"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public async Task<string> CallStatusByXML(string url, string action, string message)
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("p1", action);
            parameters.Add("p2", $"<![CDATA[{message}]]>");
            string xml = $@"<?xml version=""1.0"" encoding=""utf-8""?><soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:urn=""urn:hl7-org:v3"">
                               <soapenv:Header/>
                               <soapenv:Body>
                                  <urn:HIPMessageServer>
                                     <urn:action>{parameters["p1"]}</urn:action>
                                     <urn:message>{parameters["p2"]}</urn:message>
                                  </urn:HIPMessageServer>
                               </soapenv:Body>
                            </soapenv:Envelope>";

            using (var httpClient = new HttpClient())
            {
                var request = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xml, Encoding.UTF8, "text/xml")
                };
                request.Headers.Add("SOAPAction", "urn:HIPMessageServer");
                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(), "检查申请新增soap xml【CallByXML】request：" + xml);
                HttpResponseMessage response = await httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                string resp = await response.Content.ReadAsStringAsync();

                return resp;
            }
        }

        public ResultData ExamChangeNotifyDetail(ExamChangeNotify dtoModel, string operateId, string operateName, string operateTime)
        {
            //对象转json
            string applyInfoJson = JsonConvert.SerializeObject(dtoModel);
            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "接收申请单状态信息【ExamChangeNotifyDetail】入参：" + applyInfoJson);

            ResultData dto = new ResultData();
            dto.DataCode = "AE";
            try
            {
                if (dtoModel == null)
                {
                    dto.DataMsg = "接收数据为空";
                    return dto;
                }
                if (string.IsNullOrEmpty(dtoModel.PatientId))
                {
                    dto.DataMsg = "接收患者ID为空";
                    return dto;
                }
                if (string.IsNullOrEmpty(dtoModel.DocDetailedNo))
                {
                    dto.DataMsg = "接收申请单号为空";
                    return dto;
                }
                if (string.IsNullOrEmpty(dtoModel.StudyStatus))
                {
                    dto.DataMsg = "接收申请单状态为空";
                    return dto;
                }

                //预约平台接收医技系统发来的电子申请单状态及内容变化 接口地址
                string ExamAddress = config["Url:ExamChangeNotify"];//申请单状态及内容变化接口(是我们自己的)
                dto.DocDetailedNo = dtoModel.DocDetailedNo;
                if (!string.IsNullOrEmpty(ExamAddress))
                {
                    #region 获取token
                    string token = GetToken();
                    #endregion 获取token

                    if (!string.IsNullOrEmpty(token))
                    {
                        var applyInfo = db.ApplyInfo.Where(t => t.DocDetailedNo == dtoModel.DocDetailedNo).FirstOrDefault();
                        if (applyInfo != null)
                        {

                            if (applyInfo.OtherBZ?.Contains("床旁") ?? false)
                            {
                                #region 床旁处理
                                var apInfo = db.ApplyInfo.Where(d => d.PatientID == applyInfo.PatientID
                                                        && d.ExecutiveDepNo == applyInfo.ExecutiveDepNo
                                                        && d.ProjectNo == "01267"//床旁超声项目编码
                                                        && d.CreateTime.Value.Date == applyInfo.CreateTime.Value.Date).ToList();
                                apInfo.Add(applyInfo);
                                ResponseDto responseDto = new ResponseDto();
                                foreach (var item in apInfo)
                                {
                                    token = GetToken();
                                    if (string.IsNullOrEmpty(token))
                                    {
                                        LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"单号:{item.DocDetailedNo},token获取异常");
                                        dto.DataMsg = $"单号:{item.DocDetailedNo},token获取异常";
                                        return dto;
                                    }
                                    ExamChangeNotify examChange = new ExamChangeNotify();
                                    examChange.PatientId = item.PatientID;
                                    examChange.DocDetailedNo = item.DocDetailedNo;
                                    examChange.StudyStatus = "3";
                                    examChange.ReportTime = operateTime;
                                    examChange.ReportUser = operateName;//操作人
                                    examChange.ReportUserId = operateId;//操作人工号
                                    string applyInfoJson1 = JsonConvert.SerializeObject(examChange);
                                    //请求webapi接口，修改申请单状态
                                    string res = HttpExamChangeNotify(ExamAddress, applyInfoJson1, token, LogNames.ExamChangeNotify.ToString());
                                    responseDto = JsonConvert.DeserializeObject<ResponseDto>(res);
                                    if (responseDto.ResultCode == "0")
                                    {
                                        dto.DataCode = "AA";
                                        dto.DataMsg = "成功";
                                        continue;
                                    }
                                    else
                                    {
                                        dto.DataMsg = responseDto.ResultContent;
                                        return dto;
                                    }
                                }
                                #endregion
                            }
                            else
                            {
                                #region 常规主要处理
                                if (dtoModel.StudyStatus == StatusEnum.ReportAudit.GetDescription() ||
                                    dtoModel.StudyStatus == StatusEnum.CancelRegister.GetDescription())
                                {
                                    ExamChangeNotify examChange = new ExamChangeNotify();
                                    if (dtoModel.StudyStatus == StatusEnum.ReportAudit.GetDescription())
                                    {
                                        examChange.StudyStatus = "3";
                                    }
                                    else if (dtoModel.StudyStatus == StatusEnum.CancelRegister.GetDescription())
                                    {
                                        var styStatus = db.ApplyAfterDealInfo.Where(d => d.AppFormNo == dtoModel.DocDetailedNo).Select(d => d.StudyStatus).FirstOrDefault();
                                        if (styStatus != "3")
                                        {
                                            examChange.StudyStatus = "10";
                                        }
                                        else
                                        {
                                            dto.DataCode = "AA";
                                            dto.DataMsg = "已检查状态不变更";
                                            return dto;
                                        }
                                    }
                                    //examChange.PatientId = dtoModel.PatientId;
                                    examChange.DocDetailedNo = dtoModel.DocDetailedNo;
                                    examChange.ReportTime = operateTime;
                                    examChange.ReportUser = operateName;//操作人
                                    examChange.ReportUserId = operateId;//操作人工号
                                    string applyInfoJson1 = JsonConvert.SerializeObject(examChange);
                                    //请求webapi接口，修改申请单状态
                                    string res = HttpExamChangeNotify(ExamAddress, applyInfoJson1, token, LogNames.ExamChangeNotify.ToString());
                                    ResponseDto responseDto = JsonConvert.DeserializeObject<ResponseDto>(res);
                                    if (responseDto.ResultCode == "0")
                                    {
                                        dto.DataCode = "AA";
                                        dto.DataMsg = "成功";
                                        return dto;
                                    }
                                    else
                                    {
                                        dto.DataMsg = responseDto.ResultContent;
                                        return dto;
                                    }
                                }
                                #endregion
                            }
                        }
                        dto.DataCode = "AA";
                        dto.DataMsg = "成功";
                        dto.DocDetailedNo = dtoModel.DocDetailedNo;
                    }
                    else
                    {
                        dto.DataMsg = "token获取异常";
                        return dto;
                    }
                }
                else
                {
                    dto.DataMsg = "请求接口地址未配置";
                    return dto;
                }
            }
            catch (Exception ex)
            {
                dto.DataMsg = "程序异常";
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }

            return dto;
        }

        private string GetToken()
        {
            string clientName = config["AppSettings:ClientName"];
            var clientInfo = db.ClientInfo.FirstOrDefault(a => a.ClientName == clientName);
            var InDate = DateTime.Now.ToString("yyyyMMddHHmmss");
            var myDigest = EncryptionCodingTool.HashSHACode(clientName + InDate + clientInfo.ClientCode);
            var authStr = EncryptionCodingTool.Encrypt3DES(clientInfo.ClientCode + "$" + InDate + "$" + myDigest, clientInfo.PermitKey);
            string token = clientName + "$" + authStr;
            return token;
        }



        /// <summary>
        /// 调用webapi修改申请单状态接口
        /// </summary>
        public string HttpExamChangeNotify(string ExamAddress, string applyInfoJson, string token, string LogName)
        {
            string TFResult = string.Empty;
            //请求接口代码锁
            lock (_locker)
            {
                LogHelper.WriteInfo(LogName, $"调用ExamChangeNotify释放号源接口，入参：" + applyInfoJson);

                TFResult = RequestApi.SystrohHttpPostByToken(ExamAddress, applyInfoJson, token);

                LogHelper.WriteInfo(LogName, $"调用ExamChangeNotify释放号源接口，接口返回值：" + TFResult);// 执行需要同步的代码
            }
            return TFResult;
        }

        /// <summary>
        /// 自动把患者预约到n分钟之后的时段（项目代码,患者来源,延迟时间|||项目代码,患者来源,延迟时间）单位：分钟 实体
        /// </summary>
        public class PostponeMinSetting
        {
            public string ProjectNo { get; set; }
            public string PatientSource { get; set; }
            public string PostponeMin { get; set; }
        }

        /// <summary>
        /// 接收XML流
        /// </summary>
        /// <param name="contLeng"></param>
        /// <param name="streamXML"></param>
        /// <returns></returns>
        public string GetXMLStream(int contLeng, Stream streamXML)
        {
            StringBuilder builder = new StringBuilder();
            try
            {
                int count = 0;
                byte[] buffer = new byte[Convert.ToInt32(contLeng)];

                while ((count = streamXML.Read(buffer, 0, contLeng)) > 0)
                {
                    builder.Append(Encoding.UTF8.GetString(buffer, 0, count));
                }
                streamXML.Flush();
                streamXML.Close();
                streamXML.Dispose();
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }
            return builder.ToString();
        }

        /// <summary>
        /// 根据Transport字段规则获取最新的申请信息
        /// 规则：1. 优先选择绝对值最大的Transport
        ///       2. 如果绝对值相同，选择负值（退费）
        /// </summary>
        /// <param name="applyInfos">同一申请单号的多条数据</param>
        /// <param name="docDetailedNo">申请单号</param>
        /// <returns>最新的申请信息</returns>
        private dynamic GetLatestApplyInfoByTransport(List<ApplyInfoDto> applyInfos, string docDetailedNo)
        {
            try
            {
                // 记录所有Transport值用于日志
                var transportValues = applyInfos.Select(x => new
                {
                    Transport = x.Transport?.ToString() ?? "0",
                    DocOperCode = x.DocOperCode?.ToString() ?? "",
                    Data = x
                }).ToList();
                // 解析Transport为数值并计算绝对值
                var parsedData = transportValues.Select(x => new
                {
                    OriginalTransport = x.Transport,
                    TransportValue = int.TryParse(x.Transport, out int val) ? val : 0,
                    AbsTransportValue = Math.Abs(int.TryParse(x.Transport, out int absVal) ? absVal : 0),
                    x.DocOperCode,
                    x.Data
                }).ToList();

                // 找到绝对值最大的Transport
                var maxAbsValue = parsedData.Max(x => x.AbsTransportValue);
                var candidatesWithMaxAbs = parsedData.Where(x => x.AbsTransportValue == maxAbsValue).ToList();

                dynamic selectedApplyInfo = null;

                if (candidatesWithMaxAbs.Count == 1)
                {
                    // 只有一个最大绝对值，直接选择
                    selectedApplyInfo = candidatesWithMaxAbs.First().Data;
                }
                else
                {
                    // 多个相同的最大绝对值，优先选择负值（退费）
                    var negativeCandidate = candidatesWithMaxAbs.FirstOrDefault(x => x.TransportValue < 0 && x.DocOperCode == "CA");
                    if (negativeCandidate != null)
                    {
                        selectedApplyInfo = negativeCandidate.Data;
                    }
                    else
                    {
                        // 都是正值，选择第一个
                        selectedApplyInfo = candidatesWithMaxAbs.First().Data;
                    }
                }

                return selectedApplyInfo;
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
                LogHelper.WriteInfo(LogNames.ReceiveInfo.ToString(),$"申请单号[{docDetailedNo}]处理Transport字段异常");
                return null;
            }
        }
    }
}