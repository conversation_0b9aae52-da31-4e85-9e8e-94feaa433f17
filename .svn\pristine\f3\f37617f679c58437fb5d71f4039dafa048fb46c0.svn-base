using IntegrationPlatform.Areas.Business;
using IntegrationPlatform.Business;
using IntegrationPlatform.Common;
using IntegrationPlatform.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Quartz;
using Quartz.Spi;
using System;
using System.Threading.Tasks;
using YJAppoint.Models;
using YJAppoint.Tool;

namespace IntegrationPlatform.Areas.SendOrder.Controllers
{
    [Area("SendOrder")]
    public class ExamChangeStatusController : Controller
    {
        public ISchedulerFactory schedulerFactory { get; }
        public IJobFactory jobFactory { get; }
        public IConfiguration config { get; }
        private readonly YJAppointDbContext db;
        private readonly WebConfigBusiness webConfigBusiness;

        public ExamChangeStatusController(ISchedulerFactory _schedulerFactory, IJobFactory _jobFactory, 
            IConfiguration Configuration, YJAppointDbContext dbContext, WebConfigBusiness _webConfigBusiness)
        {
            schedulerFactory = _schedulerFactory;
            jobFactory = _jobFactory;
            config = Configuration;
            db = dbContext;
            webConfigBusiness = _webConfigBusiness;
        }

        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 判断申请单状态变更调度服务是否启动
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> IsStart()
        {
            Response dto = new Response
            {
                Code = 0
            };
            try
            {
                var examChangeStatusQuartzBusiness =
                    new ExamChangeStatusQuartzBusiness(schedulerFactory, jobFactory, config, db, webConfigBusiness);
                var isStart = await examChangeStatusQuartzBusiness.IsStart();
                if (!isStart)
                {
                    dto.Code = -1;
                }
            }
            catch (Exception e)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                dto.Code = -1;
                dto.Msg = e.Message;
            }
            return Json(dto);
        }

        /// <summary>
        /// 启动申请单状态变更调度服务
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult Start()
        {
            Response dto = new Response();
            try
            {
                var examChangeStatusQuartzBusiness =
                   new ExamChangeStatusQuartzBusiness(schedulerFactory, jobFactory, config, db, webConfigBusiness);
                examChangeStatusQuartzBusiness.Start();
                dto.Code = 0;
                dto.Msg = "成功开启申请单状态变更调度服务！每天早晨5点执行。";
                return Json(dto);
            }
            catch (Exception e)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                dto.Code = -1;
                dto.Msg = e.Message;
                return Json(dto);
            }
        }

        /// <summary>
        /// 暂停申请单状态变更调度服务
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Pause()
        {
            Response dto = new Response();
            try
            {
                var examChangeStatusQuartzBusiness =
                   new ExamChangeStatusQuartzBusiness(schedulerFactory, jobFactory, config, db, webConfigBusiness);
                await examChangeStatusQuartzBusiness.Pause();
                dto.Code = 0;
                dto.Msg = "成功暂停申请单状态变更调度服务！";
                return Json(dto);
            }
            catch (Exception e)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                dto.Code = -1;
                dto.Msg = e.Message;
                return Json(dto);
            }
        }

        /// <summary>
        /// 手动执行一次申请单状态变更任务（用于测试）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ExecuteOnce()
        {
            Response dto = new Response();
            try
            {
                var job = new ExamChangeStatusScheduleJob(null, null, config);
                await job.Execute(null);
                dto.Code = 0;
                dto.Msg = "申请单状态变更任务执行完成！";
                return Json(dto);
            }
            catch (Exception e)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                dto.Code = -1;
                dto.Msg = e.Message;
                return Json(dto);
            }
        }
    }
}
