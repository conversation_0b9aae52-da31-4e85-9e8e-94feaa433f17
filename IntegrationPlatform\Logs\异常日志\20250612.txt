
  
记录时间：2025-06-12 10:46:15,874 
线程ID:[5] 
级别:ERROR ErrorLog 
 [(null)] - Error
异常信息:System.AggregateException: One or more errors occurred. (A task was canceled.)
 ---> System.Threading.Tasks.TaskCanceledException: A task was canceled.
   --- End of inner exception stack trace ---
   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   at System.Threading.Tasks.Task`1.GetResultCore(Boolean waitCompletionNotification)
   at System.Threading.Tasks.Task`1.get_Result()
   at IntegrationPlatform.Areas.Business.IntegrateSiceBusiness.PushIntegrationRequest(String integrationUrl, ApplyInfoDto applyInfo, PatientInfoDto patientInfo, String docDetailedNoStr) in D:\work space\project\四部\北京西苑医院\IntegrationPlatform\Areas\ReceiveOrder\Business\IntegrateSiceBusiness.cs:line 901
 
