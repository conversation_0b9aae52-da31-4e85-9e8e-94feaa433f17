
 
记录时间：2025-06-10 09:56:21,569 
线程ID:[4] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:56:21

 
记录时间：2025-06-10 09:56:22,109 
线程ID:[4] ExamChangeNotify 
 -未配置执行科室和项目编号，跳过定时任务执行

 
记录时间：2025-06-10 09:56:46,265 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:56:46

 
记录时间：2025-06-10 09:56:46,351 
线程ID:[28] ExamChangeNotify 
 -未配置执行科室和项目编号，跳过定时任务执行

 
记录时间：2025-06-10 09:56:52,932 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:56:52

 
记录时间：2025-06-10 09:56:52,974 
线程ID:[28] ExamChangeNotify 
 -未配置执行科室和项目编号，跳过定时任务执行

 
记录时间：2025-06-10 09:56:59,217 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:56:59

 
记录时间：2025-06-10 09:56:59,258 
线程ID:[28] ExamChangeNotify 
 -未配置执行科室和项目编号，跳过定时任务执行

 
记录时间：2025-06-10 09:57:35,230 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:57:35

 
记录时间：2025-06-10 09:57:35,266 
线程ID:[28] ExamChangeNotify 
 -未配置执行科室和项目编号，跳过定时任务执行

 
记录时间：2025-06-10 09:57:49,427 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:57:49

 
记录时间：2025-06-10 09:59:43,225 
线程ID:[11] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:59:43

 
记录时间：2025-06-10 09:59:53,179 
线程ID:[11] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND A.ExecutiveDepNo IN ('30601') OR A.ProjectNo IN ('*7041065')

 
记录时间：2025-06-10 09:59:53,179 
线程ID:[11] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND A.ExecutiveDepNo IN ('30601') OR A.ProjectNo IN ('*7041065')
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND A.ExecutiveDepNo IN ('30601') OR A.ProjectNo IN ('*7041065')
