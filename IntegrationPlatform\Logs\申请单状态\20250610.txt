
 
记录时间：2025-06-10 09:56:21,569 
线程ID:[4] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:56:21

 
记录时间：2025-06-10 09:56:22,109 
线程ID:[4] ExamChangeNotify 
 -未配置执行科室和项目编号，跳过定时任务执行

 
记录时间：2025-06-10 09:56:46,265 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:56:46

 
记录时间：2025-06-10 09:56:46,351 
线程ID:[28] ExamChangeNotify 
 -未配置执行科室和项目编号，跳过定时任务执行

 
记录时间：2025-06-10 09:56:52,932 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:56:52

 
记录时间：2025-06-10 09:56:52,974 
线程ID:[28] ExamChangeNotify 
 -未配置执行科室和项目编号，跳过定时任务执行

 
记录时间：2025-06-10 09:56:59,217 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:56:59

 
记录时间：2025-06-10 09:56:59,258 
线程ID:[28] ExamChangeNotify 
 -未配置执行科室和项目编号，跳过定时任务执行

 
记录时间：2025-06-10 09:57:35,230 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:57:35

 
记录时间：2025-06-10 09:57:35,266 
线程ID:[28] ExamChangeNotify 
 -未配置执行科室和项目编号，跳过定时任务执行

 
记录时间：2025-06-10 09:57:49,427 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:57:49

 
记录时间：2025-06-10 09:59:43,225 
线程ID:[11] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 9:59:43

 
记录时间：2025-06-10 09:59:53,179 
线程ID:[11] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND A.ExecutiveDepNo IN ('30601') OR A.ProjectNo IN ('*7041065')

 
记录时间：2025-06-10 09:59:53,179 
线程ID:[11] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND A.ExecutiveDepNo IN ('30601') OR A.ProjectNo IN ('*7041065')
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND A.ExecutiveDepNo IN ('30601') OR A.ProjectNo IN ('*7041065')

 
记录时间：2025-06-10 11:05:17,211 
线程ID:[4] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/10 11:05:17

 
记录时间：2025-06-10 11:05:31,347 
线程ID:[4] ExamChangeNotify 
 -解析规则：科室[30601]的所有项目

 
记录时间：2025-06-10 11:05:35,505 
线程ID:[4] ExamChangeNotify 
 -解析规则：科室[30602]的所有项目

 
记录时间：2025-06-10 11:06:17,210 
线程ID:[4] ExamChangeNotify 
 -解析规则：科室[30302]的特定项目[*7041065,*7041099]

 
记录时间：2025-06-10 11:06:22,861 
线程ID:[4] ExamChangeNotify 
 -解析规则：科室[30303]的特定项目[*7041060,*7041061]

 
记录时间：2025-06-10 11:06:47,063 
线程ID:[4] ExamChangeNotify 
 -OR条件：A.ExecutiveDepNo = '30601' OR A.ExecutiveDepNo = '30602' OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')) OR (A.ExecutiveDepNo = '30303' AND A.ProjectNo IN ('*7041060','*7041061'))

 
记录时间：2025-06-10 11:06:58,320 
线程ID:[4] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo = '30601' OR A.ExecutiveDepNo = '30602' OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')) OR (A.ExecutiveDepNo = '30303' AND A.ProjectNo IN ('*7041060','*7041061')))

 
记录时间：2025-06-10 11:06:58,873 
线程ID:[4] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo = '30601' OR A.ExecutiveDepNo = '30602' OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')) OR (A.ExecutiveDepNo = '30303' AND A.ProjectNo IN ('*7041060','*7041061')))
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo = '30601' OR A.ExecutiveDepNo = '30602' OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')) OR (A.ExecutiveDepNo = '30303' AND A.ProjectNo IN ('*7041060','*7041061')))

 
记录时间：2025-06-10 11:11:13,132 
线程ID:[4] ExamChangeNotify 
 -查询时间范围: 2022-06-01 00:00:00 到 2022-06-01 23:59:59
