#pragma checksum "D:\work space\project\四部\北京西苑医院\IntegrationPlatform\Views\Home\Index.cshtml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "e6ee82ace8b497163293641e4b8bdacb269b22cc"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCore.Views_Home_Index), @"mvc.1.0.view", @"/Views/Home/Index.cshtml")]
namespace AspNetCore
{
    #line hidden
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.Rendering;
    using Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\四部\北京西苑医院\IntegrationPlatform\Views\_ViewImports.cshtml"
using IntegrationPlatform;

#line default
#line hidden
#nullable disable
#nullable restore
#line 2 "D:\work space\project\四部\北京西苑医院\IntegrationPlatform\Views\_ViewImports.cshtml"
using IntegrationPlatform.Models;

#line default
#line hidden
#nullable disable
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"e6ee82ace8b497163293641e4b8bdacb269b22cc", @"/Views/Home/Index.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"0d943924eec506c7afd2b652b926dd0cb9e61f6b", @"/Views/_ViewImports.cshtml")]
    public class Views_Home_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("media", new global::Microsoft.AspNetCore.Html.HtmlString("all"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-layout-body"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\四部\北京西苑医院\IntegrationPlatform\Views\Home\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable
            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e6ee82ace8b497163293641e4b8bdacb269b22cc5735", async() => {
                WriteLiteral(@"
    <meta charset=""utf-8"">
    <title>系统管理</title>
    <meta name=""renderer"" content=""webkit"">
    <meta http-equiv=""X-UA-Compatible"" content=""IE=edge,chrome=1"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"">
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "e6ee82ace8b497163293641e4b8bdacb269b22cc6307", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "e6ee82ace8b497163293641e4b8bdacb269b22cc7573", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e6ee82ace8b497163293641e4b8bdacb269b22cc9543", async() => {
                WriteLiteral(@"

    <div id=""LAY_app"">
        <div class=""layui-layout layui-layout-admin"">
            <div class=""layui-header"">
                <!-- 头部区域 -->
                <ul class=""layui-nav layui-layout-left"">
                    <li class=""layui-nav-item layadmin-flexible"" lay-unselect>
                        <a href=""javascript:;"" layadmin-event=""flexible"" title=""侧边伸缩"">
                            <i class=""layui-icon layui-icon-shrink-right"" id=""LAY_app_flexible""></i>
                        </a>
                    </li>

                    <li class=""layui-nav-item"" lay-unselect>
                        <a href=""javascript:;"" layadmin-event=""refresh"" title=""刷新"">
                            <i class=""layui-icon layui-icon-refresh-3""></i>
                        </a>
                    </li>
                </ul>
                <ul class=""layui-nav layui-layout-right"" lay-filter=""layadmin-layout-right"">

                    <li class=""layui-nav-item layui-hide-xs"" lay-unselect>
       ");
                WriteLiteral(@"                 <a href=""javascript:;"" layadmin-event=""theme"">
                            <i class=""layui-icon layui-icon-theme""></i>
                        </a>
                    </li>
                    <li class=""layui-nav-item layui-hide-xs"" lay-unselect>
                        <a href=""javascript:;"" layadmin-event=""note"">
                            <i class=""layui-icon layui-icon-note""></i>
                        </a>
                    </li>
                    <li class=""layui-nav-item layui-hide-xs"" lay-unselect>
                        <a href=""javascript:;"" layadmin-event=""fullscreen"">
                            <i class=""layui-icon layui-icon-screen-full""></i>
                        </a>
                    </li>
                    <li class=""layui-nav-item"" lay-unselect>
                        <a href=""javascript:;"">
                            <cite>");
#nullable restore
#line 53 "D:\work space\project\四部\北京西苑医院\IntegrationPlatform\Views\Home\Index.cshtml"
                             Write(ViewBag.Username);

#line default
#line hidden
#nullable disable
                WriteLiteral(@"</cite>
                        </a>
                        <dl class=""layui-nav-child"">
                            <dd id=""logout"" style=""text-align: center;""><a>退出</a></dd>
                        </dl>
                    </li>
                </ul>
            </div>

            <!-- 侧边菜单 -->
            <div class=""layui-side layui-side-menu"">
                <div class=""layui-side-scroll"">
                    <div class=""layui-logo"" lay-href=""#"">
                        <span>系统管理</span>
                    </div>

                    <ul class=""layui-nav layui-nav-tree"" lay-shrink=""all"" id=""LAY-system-side-menu"" lay-filter=""layadmin-system-side-menu"">
                        <li data-name=""home"" class=""layui-nav-item layui-nav-itemed"">
                            <a href=""javascript:;"" lay-tips=""主页"" lay-direction=""2"">
                                <i class=""layui-icon layui-icon-home""></i>
                                <cite>主页</cite>
                            </a>
      ");
                WriteLiteral(@"                      <dl class=""layui-nav-child"">
                                <dd data-name=""distribute"">
                                    <a lay-href=""/SendOrder/Distribute/Index"">消息分发服务</a>
                                </dd>
                                <dd data-name=""distribute"">
                                    <a lay-href=""/SendOrder/YJ_AutoSchedule/index"">自动预约服务</a>
                                </dd>
                                <dd data-name=""distribute"">
                                    <a lay-href=""/SendOrder/ExamChangeStatus/index"">申请单状态变更服务</a>
                                </dd>
");
                WriteLiteral(@"                            </dl>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 页面标签 -->
            <div class=""layadmin-pagetabs"" id=""LAY_app_tabs"">
                <div class=""layui-icon layadmin-tabs-control layui-icon-prev"" layadmin-event=""leftPage""></div>
                <div class=""layui-icon layadmin-tabs-control layui-icon-next"" layadmin-event=""rightPage""></div>
                <div class=""layui-icon layadmin-tabs-control layui-icon-down"">
                    <ul class=""layui-nav layadmin-tabs-select"" lay-filter=""layadmin-pagetabs-nav"">
                        <li class=""layui-nav-item"" lay-unselect>
                            <a href=""javascript:;""></a>
                            <dl class=""layui-nav-child layui-anim-fadein"">
                                <dd layadmin-event=""closeThisTabs""><a href=""javascript:;"">关闭当前标签页</a></dd>
                                <dd layadmin-event=""closeOtherTabs""><a href=""javasc");
                WriteLiteral(@"ript:;"">关闭其它标签页</a></dd>
                                <dd layadmin-event=""closeAllTabs""><a href=""javascript:;"">关闭全部标签页</a></dd>
                            </dl>
                        </li>
                    </ul>
                </div>
                <div class=""layui-tab"" lay-unauto lay-allowClose=""true"" lay-filter=""layadmin-layout-tabs"">
                    <ul class=""layui-tab-title"" id=""LAY_app_tabsheader"">
                        <li lay-id=""/home/<USER>"" lay-attr=""/home/<USER>"" class=""layui-this""><i class=""layui-icon layui-icon-home""></i></li>
                    </ul>
                </div>
            </div>

            <!-- 主体内容 -->
            <div class=""layui-body"" id=""LAY_app_body"">
                <div class=""layadmin-tabsbody-item layui-show"">
                    <iframe src=""/home/<USER>"" frameborder=""0"" class=""layadmin-iframe""></iframe>
                </div>
            </div>

            <!-- 辅助元素，一般用于移动设备下遮罩 -->
            <div class=""layadmin-body-shade");
                WriteLiteral("\" layadmin-event=\"shade\"></div>\r\n        </div>\r\n    </div>\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e6ee82ace8b497163293641e4b8bdacb269b22cc16117", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.config({
            base: '../layuiadmin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index'], function () {
            var $ = layui.$, form = layui.form;
            $(""#logout"").click(function () {
                $.ajax({
                    url: ""/Home/Logout"",
                    type: ""get"",
                    success: function (res) {
                        layer.msg(res.msg,
                            { time: 2000 },
                            function () {
                                location.reload();
                            });
                    }
                });
            });

        });
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>");
        }
        #pragma warning restore 1998
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; }
    }
}
#pragma warning restore 1591
