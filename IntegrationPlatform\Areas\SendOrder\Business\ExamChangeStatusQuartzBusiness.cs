using IntegrationPlatform.Business;
using IntegrationPlatform.Common;
using IntegrationPlatform.QuartzJob;
using Microsoft.Extensions.Configuration;
using Quartz;
using Quartz.Impl;
using Quartz.Spi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using YJAppoint.Models;
using YJAppoint.Tool;

namespace IntegrationPlatform.Areas.Business
{
    public class ExamChangeStatusQuartzBusiness
    {
        public IConfiguration config { get; }
        private readonly YJAppointDbContext _dbContext;
        private readonly WebConfigBusiness _webConfigBusiness;
        public ISchedulerFactory schedulerFactory { get; }
        public IJobFactory jobFactory { get; }

        public ExamChangeStatusQuartzBusiness( //IServiceProvider serviceProvider,
            ISchedulerFactory _schedulerFactory, IJobFactory _iJobFactory, IConfiguration Configuration,
               YJAppointDbContext dbContext, WebConfigBusiness webConfigBusiness)
        {
            //_serviceProvider = serviceProvider;
            schedulerFactory = _schedulerFactory;
            jobFactory = _iJobFactory;
            config = Configuration;
            _dbContext = dbContext;
            _webConfigBusiness = webConfigBusiness;
        }

        /// <summary>
        /// 启动申请单状态变更调度服务
        /// </summary>
        public async void Start()
        {
            LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "启动申请单状态变更作业调度服务");
            try
            {
                string DistributeTime = config["AppSettings:ExamChangeStatusSchedule:ScheduleTime"] + "";
                //StdSchedulerFactory factory = new StdSchedulerFactory();
                //IScheduler scheduler = await factory.GetScheduler();
                //scheduler.JobFactory = new JobFactory(_serviceProvider);
                IScheduler scheduler = await schedulerFactory.GetScheduler();
                scheduler.JobFactory = jobFactory;
                TriggerState state = await scheduler.GetTriggerState(new TriggerKey("ExamChangeStatusScheduleTrigger"));
                if (state == TriggerState.None) //状态为none则创建作业调度
                {
                    await scheduler.Start();
                    var job = JobBuilder.Create<ExamChangeStatusScheduleJob>()
                        .WithIdentity("ExamChangeStatusScheduleJob")
                        .Build();

                    // 每天早晨5点执行的Cron表达式：秒 分 时 日 月 周 年
                    var trigger = TriggerBuilder.Create()
                        .WithIdentity("ExamChangeStatusScheduleTrigger")
                        .WithCronSchedule(DistributeTime) // 定时执行时间
                        .Build();
                    await scheduler.ScheduleJob(job, trigger);
                    LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "申请单状态变更作业调度已创建");
                }
                else
                {
                    //不为none(5)则重启作业调度
                    await scheduler.ResumeTrigger(new TriggerKey("ExamChangeStatusScheduleTrigger"));
                    LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "申请单状态变更作业调度重启");
                }
                _webConfigBusiness.UpdateValueByKey("ExamChangeStatus_Distribution", "1");
            }
            catch (Exception e)
            {
                LogHelper.WriteErrorLog($"申请单状态变更调度服务异常-{LogNames.ErrorLog.ToString()}", e);
            }
        }

        public async Task<bool> IsStart()
        {
            //StdSchedulerFactory factory = new StdSchedulerFactory();
            //IScheduler scheduler = await factory.GetScheduler();
            //scheduler.JobFactory = new JobFactory(_serviceProvider);
            IScheduler scheduler = await schedulerFactory.GetScheduler();
            scheduler.JobFactory = jobFactory;
            TriggerState state = await scheduler.GetTriggerState(new TriggerKey("ExamChangeStatusScheduleTrigger"));
            if (state == TriggerState.Normal)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 暂停申请单状态变更调度服务
        /// </summary>
        public async Task Pause()
        {
            LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "暂停申请单状态变更调度服务");
            try
            {
                //StdSchedulerFactory factory = new StdSchedulerFactory();
                //IScheduler scheduler = await factory.GetScheduler();
                //scheduler.JobFactory = new JobFactory(_serviceProvider);
                IScheduler scheduler = await schedulerFactory.GetScheduler();
                scheduler.JobFactory = jobFactory;
                await scheduler.PauseTrigger(new TriggerKey("ExamChangeStatusScheduleTrigger"));
                _webConfigBusiness.UpdateValueByKey("ExamChangeStatus_Distribution", "0");
                LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "申请单状态变更调度服务已暂停");
            }
            catch (Exception e)
            {
                LogHelper.WriteErrorLog($"暂停申请单状态变更调度服务异常-{LogNames.ErrorLog.ToString()}", e);
            }
        }

        /// <summary>
        /// 检查是否需要启动申请单状态变更调度服务
        /// </summary>
        public void IsDistribution()
        {
            LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "检查申请单状态变更调度服务启动配置");
            // 检查配置文件中的启动设置
            //     string examChangeStatusQuartzIsStart = config["AppSettings:ExamChangeStatusSchedule:QuartzIsStart"] + "";
            // if (examChangeStatusQuartzIsStart.ToLower() == "true")
            // {
            //     LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "配置文件设置为启动时开启申请单状态变更调度服务");
            //     Start();
            //     return;
            // }

            // 检查数据库中的分发状态设置
            //var isDistribution = _dbContext.WebConfig.FirstOrDefault(w => w.WebKey == "ExamChangeStatus_Distribution");
            // if (isDistribution != null && isDistribution.WebValue == "1")
            // {
            //     LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "数据库配置为启动申请单状态变更调度服务");
            //     Start();
            // }
            // else
            // {
            //     LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "申请单状态变更调度服务未设置为自动启动");
            // }

            LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "检查申请单状态变更调度服务启动即开起");
            var isDistribution = config["AppSettings:ExamChangeStatusSchedule:QuartzIsStart"] == null ? "" : config["AppSettings:ExamChangeStatusSchedule:QuartzIsStart"].ToString();
            if (isDistribution == "true")
            {
                Start();
            }
        }
    }
}