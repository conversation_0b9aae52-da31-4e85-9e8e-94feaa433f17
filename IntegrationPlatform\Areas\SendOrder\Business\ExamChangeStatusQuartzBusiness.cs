using IntegrationPlatform.Business;
using IntegrationPlatform.Common;
using IntegrationPlatform.QuartzJob;
using Microsoft.Extensions.Configuration;
using Quartz;
using Quartz.Impl;
using Quartz.Spi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using YJAppoint.Models;
using YJAppoint.Tool;

namespace IntegrationPlatform.Areas.Business
{
    public class ExamChangeStatusQuartzBusiness
    {
        public IConfiguration config { get; }
        private readonly YJAppointDbContext _dbContext;
        private readonly WebConfigBusiness _webConfigBusiness;
        public ISchedulerFactory schedulerFactory { get; }
        public IJobFactory jobFactory { get; }

        public ExamChangeStatusQuartzBusiness( //IServiceProvider serviceProvider,
            ISchedulerFactory _schedulerFactory, IJobFactory _iJobFactory, IConfiguration Configuration,
               YJAppointDbContext dbContext, WebConfigBusiness webConfigBusiness)
        {
            //_serviceProvider = serviceProvider;
            schedulerFactory = _schedulerFactory;
            jobFactory = _iJobFactory;
            config = Configuration;
            _dbContext = dbContext;
            _webConfigBusiness = webConfigBusiness;
        }

        public async void Start()
        {
            int DistributeTime = Convert.ToInt32(config["AppSettings:ExamChangeStatusTime"] + "");
            //StdSchedulerFactory factory = new StdSchedulerFactory();
            //IScheduler scheduler = await factory.GetScheduler();
            //scheduler.JobFactory = new JobFactory(_serviceProvider);
            IScheduler scheduler = await schedulerFactory.GetScheduler();
            scheduler.JobFactory = jobFactory;
            TriggerState state = await scheduler.GetTriggerState(new TriggerKey("CS_AutoScheduleTrigger"));
            if (state == TriggerState.None) //状态为none则创建作业调度
            {
                await scheduler.Start();
                var job = JobBuilder.Create<ExamChangeStatusScheduleJob>()
                    .WithIdentity("ExamChangeStatusScheduleJob")
                    .Build();
                var trigger = TriggerBuilder.Create()
                    .WithIdentity("ExamChangeStatusScheduleTrigger")
                    .StartNow()
                    .WithSimpleSchedule(x => x
                        .WithIntervalInSeconds(DistributeTime)
                        .RepeatForever())
                    .Build();
                await scheduler.ScheduleJob(job, trigger);
            }
            else
            {
                //不为none(5)则重启作业调度
                await scheduler.ResumeTrigger(new TriggerKey("ExamChangeStatusScheduleTrigger"));
            }
            _webConfigBusiness.UpdateValueByKey("CS_Distribution", "1");
        }
        public async Task<bool> IsStart()
        {
            //StdSchedulerFactory factory = new StdSchedulerFactory();
            //IScheduler scheduler = await factory.GetScheduler();
            //scheduler.JobFactory = new JobFactory(_serviceProvider);
            IScheduler scheduler = await schedulerFactory.GetScheduler();
            scheduler.JobFactory = jobFactory;
            TriggerState state = await scheduler.GetTriggerState(new TriggerKey("ExamChangeStatusScheduleTrigger"));
            if (state == TriggerState.Normal)
            {
                return true;
            }
            return false;
        }

        public async Task Pause()
        {
            //StdSchedulerFactory factory = new StdSchedulerFactory();
            //IScheduler scheduler = await factory.GetScheduler();
            //scheduler.JobFactory = new JobFactory(_serviceProvider);
            IScheduler scheduler = await schedulerFactory.GetScheduler();
            scheduler.JobFactory = jobFactory;
            await scheduler.PauseTrigger(new TriggerKey("ExamChangeStatusScheduleTrigger"));
            _webConfigBusiness.UpdateValueByKey("CS_Distribution", "0");
        }

        public void IsDistribution()
        {
            LogHelper.WriteInfo(LogNames.WebInfo.ToString(), "启动即开起");
            var isDistribution = _dbContext.WebConfig.FirstOrDefault(w => w.WebKey == "CS_Distribution");
            if (isDistribution != null && isDistribution.WebValue == "1")
            {
                Start();
            }
        }
    }
}