
 
记录时间：2025-06-12 10:44:04,813 
线程ID:[5] ReceiveInfo 
 -{"Request":{"MesType":"0","PatientInfo":{"PatientID":"001526752700","PatientCode":"98020000798903","BLH":"","BRKH":"98020000798903","TreatmentNo":null,"SFZH":"132523197901162838","PatientNo":null,"PatientName":"刘明生","BirthDay":"19790116","Age":"46","Sex":"男","PatientSource":"O","ChargeType":"公费","ChargeNo":null,"HospitalStatus":null,"AttendingDoctor":null,"AttendingDoctorNo":null,"Department":"综合内科门诊","DepartmentNo":"1111410","DiagnosisNo":null,"Diagnosis":null,"RyTime":null,"CyTime":null,"BedNumber":"","InpatientArea":"","InpatientAreaNo":"","Adress":"","Telephone":"13520146379","Marriage":"未婚","Nation":"汉族","Nationality":"中国","OtherBZ":"","GetMessTim":null},"AlyList":{"ApplyInformation":[{"RegisterID":null,"DocOperCode":"NW","DocDetailedNo":"7543138","DocGroupNo":"7543138","AcessionNo":null,"DocOrderCif":null,"DocInputNo":null,"DocInputName":null,"InputMark":null,"DocScheduleNo":null,"DocScheduleNaeme":null,"DocTAakeEfDate":null,"DocMarkNo":"50297","DocMarkName":"张颖","DocMarkDecNo":null,"DocMarkDecName":null,"ClinicSign":null,"DocSchedeleDate":"2025-06-10 10:17:35","ExecutiveDepNo":"1400405","ExecutiveDepName":"超声检查科","ProjectState":null,"ProjectNo":"01321","ProjectName":"超声心动图","ProjectClass":null,"ProjectCount":"1","ProjectCom":null,"ProjectPrice":"493.0","ChargeSign":null,"PriceNumber":null,"ChargeNumber":null,"InvoiceNub":null,"DocAdvice":null,"AplicationForm":"","ApplyDep":"综合内科门诊","ApplyDepNo":"1111410","OtherBZ":"","HospitalAreaCode":"01","StudyNo":null,"Times":"1","AppendMessage":"协助诊断","JCBW":null,"Transport":"666","SQMD":null,"BEDSIDE":null,"MDRO":null,"MrSummary":null,"if_kf":null},{"RegisterID":null,"DocOperCode":"NW","DocDetailedNo":"7543138","DocGroupNo":"7543138","AcessionNo":null,"DocOrderCif":null,"DocInputNo":null,"DocInputName":null,"InputMark":null,"DocScheduleNo":null,"DocScheduleNaeme":null,"DocTAakeEfDate":null,"DocMarkNo":"50297","DocMarkName":"张颖","DocMarkDecNo":null,"DocMarkDecName":null,"ClinicSign":null,"DocSchedeleDate":"2025-06-10 10:17:35","ExecutiveDepNo":"1400405","ExecutiveDepName":"超声检查科","ProjectState":null,"ProjectNo":"01321","ProjectName":"超声心动图","ProjectClass":null,"ProjectCount":"1","ProjectCom":null,"ProjectPrice":"493.0","ChargeSign":null,"PriceNumber":null,"ChargeNumber":null,"InvoiceNub":null,"DocAdvice":null,"AplicationForm":"","ApplyDep":"综合内科门诊","ApplyDepNo":"1111410","OtherBZ":"","HospitalAreaCode":"01","StudyNo":null,"Times":"1","AppendMessage":"协助诊断","JCBW":null,"Transport":"667","SQMD":null,"BEDSIDE":null,"MDRO":null,"MrSummary":null,"if_kf":null},{"RegisterID":null,"DocOperCode":"CA","DocDetailedNo":"7543138","DocGroupNo":"7543138","AcessionNo":null,"DocOrderCif":null,"DocInputNo":null,"DocInputName":null,"InputMark":null,"DocScheduleNo":null,"DocScheduleNaeme":null,"DocTAakeEfDate":null,"DocMarkNo":"50297","DocMarkName":"张颖","DocMarkDecNo":null,"DocMarkDecName":null,"ClinicSign":null,"DocSchedeleDate":"2025-06-10 10:17:35","ExecutiveDepNo":"1400405","ExecutiveDepName":"超声检查科","ProjectState":null,"ProjectNo":"01321","ProjectName":"超声心动图","ProjectClass":null,"ProjectCount":"1","ProjectCom":null,"ProjectPrice":"493.0","ChargeSign":null,"PriceNumber":null,"ChargeNumber":null,"InvoiceNub":null,"DocAdvice":null,"AplicationForm":"","ApplyDep":"综合内科门诊","ApplyDepNo":"1111410","OtherBZ":"","HospitalAreaCode":"01","StudyNo":null,"Times":"1","AppendMessage":"协助诊断","JCBW":null,"Transport":"-666","SQMD":null,"BEDSIDE":null,"MDRO":null,"MrSummary":null,"if_kf":null}]}}}

 
记录时间：2025-06-12 10:44:04,900 
线程ID:[5] ReceiveInfo 
 -新增患者001526752700

 
记录时间：2025-06-12 10:46:12,192 
线程ID:[5] ReceiveInfo 
 -新增申请单7543138

 
记录时间：2025-06-12 10:46:12,206 
线程ID:[5] ReceiveInfo 
 -推送CheckAppInfoAdd到集成平台的接口请求xml：<POOR_IN200901UV ITSVersion="XML_1.0" xmlns="urn:hl7-org:v3" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:hl7-org:v3 ../multicacheschemas/POOR_IN200901UV.xsd"><!-- 消息ID --><id root="2.16.156.10011.*******" extension="f5d33737-c1e0-4a75-af73-4e50fcb1cd7a" /><!-- 消息创建时间 --><creationTime value="20250612104612" /><!-- 消息的服务标识--><interactionId root="2.16.156.10011.*******" extension="POOR_IN200901UV" /><!--处理代码，标识此消息是否是产品、训练、调试系统的一部分。D：调试；P：产品；T：训练 --><processingCode code="P" /><!-- 消息处理模式: A(Archive); I(Initial load); R(Restore from archive); T(Current 
processing) --><processingModeCode /><!-- 消息应答: AL(Always); ER(Error/reject only); NE(Never) --><acceptAckCode code="AL" /><!-- 接受者 --><receiver typeCode="RCV"><device classCode="DEV" determinerCode="INSTANCE"><!-- 接受者ID --><id><item root="2.16.156.10011.2.5.1.3" extension="ESB" /></id></device></receiver><!-- 发送者 --><sender typeCode="SND"><device classCode="DEV" determinerCode="INSTANCE"><!-- 发送者ID --><id><item root="2.16.156.10011.2.5.1.3" extension="TZYT" /></id></device></sender><!-- 封装的消息内容 --><controlActProcess classCode="CACT" moodCode="EVN"><subject typeCode="SUBJ"><observationRequest classCode="OBS" moodCode="RQO"><!-- 检查申请单编号 必须项已使用 --><id><item root="2.16.156.10011.1.24" extension="7543138" /></id><code /><!-- 申请单详细内容 --><text value="" /><!-- 必须项未使用 --><statusCode /><!--检查申请有效日期时间 --><effectiveTime xsi:type="IVL_TS"><low value="" /><high value="" /></effectiveTime><!--优先（紧急）度--><priorityCode code=""><displayName value="" /></priorityCode><!--开单医生/送检医生 --><author typeCode="AUT"><!-- 开单时间 --><time value="2025-06-10 10:17:35" /><!--申请单开立者签名--><signatureText value="张颖" /><assignedEntity classCode="ASSIGNED"><!--开单医生工号 --><id><item extension="50297" root="2.16.156.10011.1.4" /></id><!--开单医生姓名 --><assignedPerson determinerCode="INSTANCE" classCode="PSN"><name xsi:type="BAG_EN"><item><part value="张颖" /></item></name></assignedPerson><!-- 申请科室信息 --><representedOrganization determinerCode="INSTANCE" classCode="ORG"><!--申请科室编码 必须项已使用 --><id><item extension="1111410" root="2.16.156.10011.1.26" /></id><!--申请科室名称 --><name xsi:type="BAG_EN"><item><part value="综合内科门诊" /></item></name></representedOrganization></assignedEntity></author><!--审核者--><verifier typeCode="VRF"><!--审核日期时间 --><time value="" /><assignedEntity classCode="ASSIGNED"><!--审核者工号 --><id><item extension="" root="2.16.156.10011.1.4" /></id><assignedPerson determinerCode="INSTANCE" classCode="PSN"><!--审核者姓名 --><name xsi:type="DSET_EN"><item><part value="" /></item></name></assignedPerson></assignedEntity></verifier><!-- 多个检查项目循环component2 --><component2><observationRequest classCode="OBS" moodCode="RQO"><id><!--医嘱ID--><item root="2.16.156.10011.1.28" extension="7543138" /></id><!--检查项目编码 必须项已使用 --><code code="01321"><!--检查项目名称 --><displayName value="超声心动图" /></code><!-- 必须项未使用 --><statusCode /><methodCode><!--检查方式编码 --><item code="" codeSystem="2.16.156.10011.2.3.2.47" codeSystemName=""><!--检查方式名称 --><displayName value="" /></item><!--检查类型编码 --><item code=""><!--检查类型名称 --><displayName value="" /></item></methodCode><!--检查部位编码 --><targetSiteCode><item code=""><!--检查部位名称 --><displayName value="" /></item></targetSiteCode><!--执行科室 --><location typeCode="LOC"><!-- 执行时间 --><time><any value="" /></time><serviceDeliveryLocation classCode="SDLOC"><serviceProviderOrganization determinerCode="INSTANCE" classCode="ORG"><!--执行科室编码 --><id><item extension="1400405" root="2.16.156.10011.1.26" /></id><!-- 执行科室名称 --><name xsi:type="DSET_EN"><item><part value="超声检查科" /></item></name></serviceProviderOrganization></serviceDeliveryLocation></location></observationRequest></component2><subjectOf6 contextConductionInd="false"><!-- 必须项 未使用 default=false --><seperatableInd value="false" /><!--申请注意事项 --><annotation><text value="" /><statusCode code="completed" /><author><assignedEntity classCode="ASSIGNED" /></author></annotation></subjectOf6><!--就诊 --><componentOf1 contextConductionInd="false" xsi:nil="false" typeCode="COMP"><!--就诊 --><encounter classCode="ENC" moodCode="EVN"><id><!-- 就诊次数 --><item extension="1" root="2.16.156.10011.2.5.1.8" /><!-- 就诊流水号 --><item extension="" root="2.16.156.10011.2.5.1.9" /></id><!--就诊类别编码--><code codeSystem="2.16.156.10011.2.3.1.271" codeSystemName="患者类型代码表" code="O"><!-- 就诊类别名称 --><displayName value="门诊" /></code><!--必须项未使用 --><statusCode /><!--病人 必须项未使用 --><subject typeCode="SBJ"><patient classCode="PAT"><id><!-- 域ID --><item root="2.16.156.10011.2.5.1.5" extension="01" /><!-- 患者ID --><item root="2.16.156.10011.2.5.1.4" extension="001526752700" /><!--门（急）诊号标识 --><item root="2.16.156.10011.1.10" extension="001526752700" /><!--住院号标识--><item root="2.16.156.10011.1.12" extension="" /></id><!--个人信息 必须项已使用 --><patientPerson classCode="PSN"><!-- 身份证号/医保卡号 --><id><!-- 身份证号 --><item extension="132523197901162838" root="2.16.156.10011.1.3" /><!-- 医保卡号 --><item extension="" root="2.16.156.10011.1.15" /></id><!--姓名 --><name xsi:type="DSET_EN"><item><part value="刘明生" /></item></name><!-- 联系电话 --><telecom xsi:type="BAG_TEL"><!-- 联系电话 --><item value="13520146379" /></telecom><!--性别代码 --><administrativeGenderCode code="1" codeSystem="2.16.156.10011.2.3.3.4" /><!--出生日期 --><birthTime value="19790116"><!--年龄 --><originalText value="46" /></birthTime><!--住址 --><addr xsi:type="BAG_AD"><item use="H"><part type="AL" value="" /></item></addr></patientPerson></patient></subject><!--住院位置--><location typeCode="LOC"><time /><serviceDeliveryLocation classCode="SDLOC"><location classCode="PLC" determinerCode="INSTANCE"><!--DE01.00.026.00 病床号--><id><item extension="" /></id><name xsi:type="BAG_EN"><item use="IDE"><part value="201" /></item></name><asLocatedEntityPartOf classCode="LOCE"><location classCode="PLC" determinerCode="INSTANCE"><!--DE01.00.019.00 病房号--><id><item extension="" /></id><name xsi:type="BAG_EN"><item use="IDE"><part value="" /></item></name></location></asLocatedEntityPartOf></location><serviceProviderOrganization classCode="ORG" determinerCode="INSTANCE"><!--DE08.10.026.00 科室名称--><id><item extension="1400405" /></id><name xsi:type="BAG_EN"><item use="IDE"><part value="超声检查科" /></item></name><asOrganizationPartOf classCode="PART"><!-- DE08.10.054.00 病区名称 --><wholeOrganization classCode="ORG" determinerCode="INSTANCE"><id><item extension="" /></id><name xsi:type="BAG_EN"><item use="IDE"><part value="" /></item></name></wholeOrganization></asOrganizationPartOf></serviceProviderOrganization></serviceDeliveryLocation></location><!--诊断(检查申请原因) --><pertinentInformation1 typeCode="PERT" xsi:nil="false"><observationDx classCode="OBS" moodCode="EVN"><!--诊断类别编码 必须项已使用 --><code code="" codeSystem="2.16.156.10011.*******0"><!--诊断类别名称 --><displayName value="" /></code><!-- 必须项未使用 --><statusCode code="active" /><!--诊断日期 --><effectiveTime><any value="" /></effectiveTime><!-- 疾病编码 必须项已使用 --><value code="" codeSystem="2.16.156.10011.********"><!-- 疾病名称 --><displayName value="" /></value></observationDx></pertinentInformation1></encounter></componentOf1></observationRequest></subject></controlActProcess><extra><ClinicalDiagnosis displayName="临床诊断"></ClinicalDiagnosis><ProjectPrice display="费用">493.0</ProjectPrice><Purpose display="检查目的">协助诊断</Purpose></extra></POOR_IN200901UV>

 
记录时间：2025-06-12 10:46:12,258 
线程ID:[5] ReceiveInfo 
 -检查申请新增soap xml【CallByXML】request：<?xml version="1.0" encoding="utf-8"?><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:urn="urn:hl7-org:v3">
                               <soapenv:Header/>
                               <soapenv:Body>
                                  <urn:HIPMessageServer>
                                     <urn:action>CheckAppInfoAdd</urn:action>
                                     <urn:message><![CDATA[<POOR_IN200901UV ITSVersion="XML_1.0" xmlns="urn:hl7-org:v3" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:hl7-org:v3 ../multicacheschemas/POOR_IN200901UV.xsd"><!-- 消息ID --><id root="2.16.156.10011.*******" extension="f5d33737-c1e0-4a75-af73-4e50fcb1cd7a" /><!-- 消息创建时间 --><creationTime value="20250612104612" /><!-- 消息的服务标识--><interactionId root="2.16.156.10011.*******" extension="POOR_IN200901UV" /><!--处理代码，标识此消息是否是产品、训练、调试系统的一部分。D：调试；P：产品；T：训练 --><processingCode code="P" /><!-- 消息处理模式: A(Archive); I(Initial load); R(Restore from archive); T(Current 
processing) --><processingModeCode /><!-- 消息应答: AL(Always); ER(Error/reject only); NE(Never) --><acceptAckCode code="AL" /><!-- 接受者 --><receiver typeCode="RCV"><device classCode="DEV" determinerCode="INSTANCE"><!-- 接受者ID --><id><item root="2.16.156.10011.2.5.1.3" extension="ESB" /></id></device></receiver><!-- 发送者 --><sender typeCode="SND"><device classCode="DEV" determinerCode="INSTANCE"><!-- 发送者ID --><id><item root="2.16.156.10011.2.5.1.3" extension="TZYT" /></id></device></sender><!-- 封装的消息内容 --><controlActProcess classCode="CACT" moodCode="EVN"><subject typeCode="SUBJ"><observationRequest classCode="OBS" moodCode="RQO"><!-- 检查申请单编号 必须项已使用 --><id><item root="2.16.156.10011.1.24" extension="7543138" /></id><code /><!-- 申请单详细内容 --><text value="" /><!-- 必须项未使用 --><statusCode /><!--检查申请有效日期时间 --><effectiveTime xsi:type="IVL_TS"><low value="" /><high value="" /></effectiveTime><!--优先（紧急）度--><priorityCode code=""><displayName value="" /></priorityCode><!--开单医生/送检医生 --><author typeCode="AUT"><!-- 开单时间 --><time value="2025-06-10 10:17:35" /><!--申请单开立者签名--><signatureText value="张颖" /><assignedEntity classCode="ASSIGNED"><!--开单医生工号 --><id><item extension="50297" root="2.16.156.10011.1.4" /></id><!--开单医生姓名 --><assignedPerson determinerCode="INSTANCE" classCode="PSN"><name xsi:type="BAG_EN"><item><part value="张颖" /></item></name></assignedPerson><!-- 申请科室信息 --><representedOrganization determinerCode="INSTANCE" classCode="ORG"><!--申请科室编码 必须项已使用 --><id><item extension="1111410" root="2.16.156.10011.1.26" /></id><!--申请科室名称 --><name xsi:type="BAG_EN"><item><part value="综合内科门诊" /></item></name></representedOrganization></assignedEntity></author><!--审核者--><verifier typeCode="VRF"><!--审核日期时间 --><time value="" /><assignedEntity classCode="ASSIGNED"><!--审核者工号 --><id><item extension="" root="2.16.156.10011.1.4" /></id><assignedPerson determinerCode="INSTANCE" classCode="PSN"><!--审核者姓名 --><name xsi:type="DSET_EN"><item><part value="" /></item></name></assignedPerson></assignedEntity></verifier><!-- 多个检查项目循环component2 --><component2><observationRequest classCode="OBS" moodCode="RQO"><id><!--医嘱ID--><item root="2.16.156.10011.1.28" extension="7543138" /></id><!--检查项目编码 必须项已使用 --><code code="01321"><!--检查项目名称 --><displayName value="超声心动图" /></code><!-- 必须项未使用 --><statusCode /><methodCode><!--检查方式编码 --><item code="" codeSystem="2.16.156.10011.2.3.2.47" codeSystemName=""><!--检查方式名称 --><displayName value="" /></item><!--检查类型编码 --><item code=""><!--检查类型名称 --><displayName value="" /></item></methodCode><!--检查部位编码 --><targetSiteCode><item code=""><!--检查部位名称 --><displayName value="" /></item></targetSiteCode><!--执行科室 --><location typeCode="LOC"><!-- 执行时间 --><time><any value="" /></time><serviceDeliveryLocation classCode="SDLOC"><serviceProviderOrganization determinerCode="INSTANCE" classCode="ORG"><!--执行科室编码 --><id><item extension="1400405" root="2.16.156.10011.1.26" /></id><!-- 执行科室名称 --><name xsi:type="DSET_EN"><item><part value="超声检查科" /></item></name></serviceProviderOrganization></serviceDeliveryLocation></location></observationRequest></component2><subjectOf6 contextConductionInd="false"><!-- 必须项 未使用 default=false --><seperatableInd value="false" /><!--申请注意事项 --><annotation><text value="" /><statusCode code="completed" /><author><assignedEntity classCode="ASSIGNED" /></author></annotation></subjectOf6><!--就诊 --><componentOf1 contextConductionInd="false" xsi:nil="false" typeCode="COMP"><!--就诊 --><encounter classCode="ENC" moodCode="EVN"><id><!-- 就诊次数 --><item extension="1" root="2.16.156.10011.2.5.1.8" /><!-- 就诊流水号 --><item extension="" root="2.16.156.10011.2.5.1.9" /></id><!--就诊类别编码--><code codeSystem="2.16.156.10011.2.3.1.271" codeSystemName="患者类型代码表" code="O"><!-- 就诊类别名称 --><displayName value="门诊" /></code><!--必须项未使用 --><statusCode /><!--病人 必须项未使用 --><subject typeCode="SBJ"><patient classCode="PAT"><id><!-- 域ID --><item root="2.16.156.10011.2.5.1.5" extension="01" /><!-- 患者ID --><item root="2.16.156.10011.2.5.1.4" extension="001526752700" /><!--门（急）诊号标识 --><item root="2.16.156.10011.1.10" extension="001526752700" /><!--住院号标识--><item root="2.16.156.10011.1.12" extension="" /></id><!--个人信息 必须项已使用 --><patientPerson classCode="PSN"><!-- 身份证号/医保卡号 --><id><!-- 身份证号 --><item extension="132523197901162838" root="2.16.156.10011.1.3" /><!-- 医保卡号 --><item extension="" root="2.16.156.10011.1.15" /></id><!--姓名 --><name xsi:type="DSET_EN"><item><part value="刘明生" /></item></name><!-- 联系电话 --><telecom xsi:type="BAG_TEL"><!-- 联系电话 --><item value="13520146379" /></telecom><!--性别代码 --><administrativeGenderCode code="1" codeSystem="2.16.156.10011.2.3.3.4" /><!--出生日期 --><birthTime value="19790116"><!--年龄 --><originalText value="46" /></birthTime><!--住址 --><addr xsi:type="BAG_AD"><item use="H"><part type="AL" value="" /></item></addr></patientPerson></patient></subject><!--住院位置--><location typeCode="LOC"><time /><serviceDeliveryLocation classCode="SDLOC"><location classCode="PLC" determinerCode="INSTANCE"><!--DE01.00.026.00 病床号--><id><item extension="" /></id><name xsi:type="BAG_EN"><item use="IDE"><part value="201" /></item></name><asLocatedEntityPartOf classCode="LOCE"><location classCode="PLC" determinerCode="INSTANCE"><!--DE01.00.019.00 病房号--><id><item extension="" /></id><name xsi:type="BAG_EN"><item use="IDE"><part value="" /></item></name></location></asLocatedEntityPartOf></location><serviceProviderOrganization classCode="ORG" determinerCode="INSTANCE"><!--DE08.10.026.00 科室名称--><id><item extension="1400405" /></id><name xsi:type="BAG_EN"><item use="IDE"><part value="超声检查科" /></item></name><asOrganizationPartOf classCode="PART"><!-- DE08.10.054.00 病区名称 --><wholeOrganization classCode="ORG" determinerCode="INSTANCE"><id><item extension="" /></id><name xsi:type="BAG_EN"><item use="IDE"><part value="" /></item></name></wholeOrganization></asOrganizationPartOf></serviceProviderOrganization></serviceDeliveryLocation></location><!--诊断(检查申请原因) --><pertinentInformation1 typeCode="PERT" xsi:nil="false"><observationDx classCode="OBS" moodCode="EVN"><!--诊断类别编码 必须项已使用 --><code code="" codeSystem="2.16.156.10011.*******0"><!--诊断类别名称 --><displayName value="" /></code><!-- 必须项未使用 --><statusCode code="active" /><!--诊断日期 --><effectiveTime><any value="" /></effectiveTime><!-- 疾病编码 必须项已使用 --><value code="" codeSystem="2.16.156.10011.********"><!-- 疾病名称 --><displayName value="" /></value></observationDx></pertinentInformation1></encounter></componentOf1></observationRequest></subject></controlActProcess><extra><ClinicalDiagnosis displayName="临床诊断"></ClinicalDiagnosis><ProjectPrice display="费用">493.0</ProjectPrice><Purpose display="检查目的">协助诊断</Purpose></extra></POOR_IN200901UV>]]></urn:message>
                                  </urn:HIPMessageServer>
                               </soapenv:Body>
                            </soapenv:Envelope>

 
记录时间：2025-06-12 10:46:16,026 
线程ID:[5] ReceiveInfo 
 -接收检查医嘱申请信息成功

 
记录时间：2025-06-12 10:46:16,034 
线程ID:[5] ReceiveInfo 
 -方法：GetDocOrders；返回：{"ResultCode":"0","ResultContent":"成功"}
