﻿@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="~/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="~/layuiadmin/style/admin.css" media="all">
</head>
<body class="layui-layout-body">

    <div id="LAY_app">
        <div class="layui-layout layui-layout-admin">
            <div class="layui-header">
                <!-- 头部区域 -->
                <ul class="layui-nav layui-layout-left">
                    <li class="layui-nav-item layadmin-flexible" lay-unselect>
                        <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩">
                            <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
                        </a>
                    </li>

                    <li class="layui-nav-item" lay-unselect>
                        <a href="javascript:;" layadmin-event="refresh" title="刷新">
                            <i class="layui-icon layui-icon-refresh-3"></i>
                        </a>
                    </li>
                </ul>
                <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right">

                    <li class="layui-nav-item layui-hide-xs" lay-unselect>
                        <a href="javascript:;" layadmin-event="theme">
                            <i class="layui-icon layui-icon-theme"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item layui-hide-xs" lay-unselect>
                        <a href="javascript:;" layadmin-event="note">
                            <i class="layui-icon layui-icon-note"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item layui-hide-xs" lay-unselect>
                        <a href="javascript:;" layadmin-event="fullscreen">
                            <i class="layui-icon layui-icon-screen-full"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item" lay-unselect>
                        <a href="javascript:;">
                            <cite>@ViewBag.Username</cite>
                        </a>
                        <dl class="layui-nav-child">
                            <dd id="logout" style="text-align: center;"><a>退出</a></dd>
                        </dl>
                    </li>
                </ul>
            </div>

            <!-- 侧边菜单 -->
            <div class="layui-side layui-side-menu">
                <div class="layui-side-scroll">
                    <div class="layui-logo" lay-href="#">
                        <span>系统管理</span>
                    </div>

                    <ul class="layui-nav layui-nav-tree" lay-shrink="all" id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">
                        <li data-name="home" class="layui-nav-item layui-nav-itemed">
                            <a href="javascript:;" lay-tips="主页" lay-direction="2">
                                <i class="layui-icon layui-icon-home"></i>
                                <cite>主页</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd data-name="distribute">
                                    <a lay-href="/SendOrder/Distribute/Index">消息分发服务</a>
                                </dd>
                                <dd data-name="distribute">
                                    <a lay-href="/SendOrder/YJ_AutoSchedule/index">自动预约服务</a>
                                </dd>
                                <dd data-name="distribute">
                                    <a lay-href="/SendOrder/ExamChangeStatus/index">申请单状态变更服务</a>
                                </dd>
                                @*<dd data-name="distribute">
            <a lay-href="/SendOrder/CS_AutoSchedule/index">超声自动预约</a>
        </dd>
        <dd data-name="distribute">
            <a lay-href="/SendOrder/CSXD_AutoSchedule/index">超声心动自动预约</a>
        </dd>
        <dd data-name="distribute">
            <a lay-href="/SendOrder/EIS_PushingApplicationForm/index">内镜中心服务</a>
        </dd>
        <dd data-name="distribute">
            <a lay-href="/SendOrder/RIS_AutoSchedule/index">放射科自动预约</a>
        </dd>
        <dd data-name="distribute">
            <a lay-href="/SendOrder/XD_AutoSchedule/index">心电自动预约</a>
        </dd>
        <dd data-name="distribute">
            <a lay-href="/SendOrder/GanB_AutoSchedule/index">肝病科自动预约</a>
        </dd>
        <dd data-name="distribute">
            <a lay-href="/SendOrder/Change_AutoSchedule/index">轮询未缴费申请单取消预约</a>
        </dd>
        <dd data-name="distribute">
            <a lay-href="/SendOrder/SendNotice/index">轮询发送已预约通知</a>
        </dd>*@
                            </dl>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 页面标签 -->
            <div class="layadmin-pagetabs" id="LAY_app_tabs">
                <div class="layui-icon layadmin-tabs-control layui-icon-prev" layadmin-event="leftPage"></div>
                <div class="layui-icon layadmin-tabs-control layui-icon-next" layadmin-event="rightPage"></div>
                <div class="layui-icon layadmin-tabs-control layui-icon-down">
                    <ul class="layui-nav layadmin-tabs-select" lay-filter="layadmin-pagetabs-nav">
                        <li class="layui-nav-item" lay-unselect>
                            <a href="javascript:;"></a>
                            <dl class="layui-nav-child layui-anim-fadein">
                                <dd layadmin-event="closeThisTabs"><a href="javascript:;">关闭当前标签页</a></dd>
                                <dd layadmin-event="closeOtherTabs"><a href="javascript:;">关闭其它标签页</a></dd>
                                <dd layadmin-event="closeAllTabs"><a href="javascript:;">关闭全部标签页</a></dd>
                            </dl>
                        </li>
                    </ul>
                </div>
                <div class="layui-tab" lay-unauto lay-allowClose="true" lay-filter="layadmin-layout-tabs">
                    <ul class="layui-tab-title" id="LAY_app_tabsheader">
                        <li lay-id="/home/<USER>" lay-attr="/home/<USER>" class="layui-this"><i class="layui-icon layui-icon-home"></i></li>
                    </ul>
                </div>
            </div>

            <!-- 主体内容 -->
            <div class="layui-body" id="LAY_app_body">
                <div class="layadmin-tabsbody-item layui-show">
                    <iframe src="/home/<USER>" frameborder="0" class="layadmin-iframe"></iframe>
                </div>
            </div>

            <!-- 辅助元素，一般用于移动设备下遮罩 -->
            <div class="layadmin-body-shade" layadmin-event="shade"></div>
        </div>
    </div>

    <script src="~/layuiadmin/layui/layui.js"></script>
    <script>
        layui.config({
            base: '../layuiadmin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index'], function () {
            var $ = layui.$, form = layui.form;
            $("#logout").click(function () {
                $.ajax({
                    url: "/Home/Logout",
                    type: "get",
                    success: function (res) {
                        layer.msg(res.msg,
                            { time: 2000 },
                            function () {
                                location.reload();
                            });
                    }
                });
            });

        });
    </script>
</body>
</html>