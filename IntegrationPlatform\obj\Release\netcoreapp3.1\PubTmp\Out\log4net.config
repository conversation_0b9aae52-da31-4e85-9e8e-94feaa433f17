﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
	<log4net>
		<!--接收医嘱信息日志配置-->
		<appender name="ReceiveInfoAppender" type="log4net.Appender.RollingFileAppender">
			<file value="Logs\接收医嘱信息日志\" />
			<appendToFile value="true" />
			<rollingStyle value="Date" />
			<datePattern value="yyyyMMdd'.txt'" />
			<staticLogFileName value="false" />
			<param name="MaxSizeRollBackups" value="100" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%newline %n记录时间：%date %n线程ID:[%thread] %logger %n -%m%n" />
			</layout>
		</appender>

		<!--自动预约日志配置-->
		<appender name="YJInfoAppender" type="log4net.Appender.RollingFileAppender">
			<file value="Logs\自动预约日志\" />
			<appendToFile value="true" />
			<rollingStyle value="Date" />
			<datePattern value="yyyyMMdd'.txt'" />
			<staticLogFileName value="false" />
			<param name="MaxSizeRollBackups" value="100" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%newline %n记录时间：%date %n线程ID:[%thread] %logger %n -%m%n" />
			</layout>
		</appender>

		<!--分发作业信息日志配置-->
		<appender name="SendInfoAppender" type="log4net.Appender.RollingFileAppender">
			<file value="Logs\消息分发日志\" />
			<appendToFile value="true" />
			<rollingStyle value="Date" />
			<datePattern value="yyyyMMdd'.txt'" />
			<staticLogFileName value="false" />
			<param name="MaxSizeRollBackups" value="100" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%newline %n记录时间：%date %n线程ID:[%thread] %logger %n -%m%n" />
			</layout>
		</appender>

		<!--患者退费日志配置-->
		<appender name="CancelFeeAppAppender" type="log4net.Appender.RollingFileAppender">
			<file value="Logs\患者退费日志\" />
			<appendToFile value="true" />
			<rollingStyle value="Date" />
			<datePattern value="yyyyMMdd'.txt'" />
			<staticLogFileName value="false" />
			<param name="MaxSizeRollBackups" value="100" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%newline %n记录时间：%date %n线程ID:[%thread] %logger %n -%m%n" />
			</layout>
		</appender>

		<!--网站日志配置-->
		<appender name="WebInfoAppender" type="log4net.Appender.RollingFileAppender">
			<file value="Logs\网站操作日志\" />
			<appendToFile value="true" />
			<rollingStyle value="Date" />
			<datePattern value="yyyyMMdd'.txt'" />
			<staticLogFileName value="false" />
			<param name="MaxSizeRollBackups" value="100" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%newline %n记录时间：%date %n线程ID:[%thread] %n类名:%logger %n-%m%n" />
			</layout>
		</appender>

		<!--异常日志-->
		<appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
			<file value="Logs\异常日志\" />
			<appendToFile value="true" />
			<rollingStyle value="Composite" />
			<staticLogFileName value="false" />
			<datePattern value="yyyyMMdd'.txt'" />
			<maxSizeRollBackups value="100" />
			<maximumFileSize value="10MB" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%newline  %n记录时间：%date %n线程ID:[%thread] %n级别:%-5level %logger %n [%property{NDC}] - %message%n异常信息:%exception %newline" />
			</layout>
		</appender>

		<appender name="ExamChangeNotifyAppender" type="log4net.Appender.RollingFileAppender">
			<file value="Logs\申请单状态\" />
			<appendToFile value="true" />
			<rollingStyle value="Date" />
			<datePattern value="yyyyMMdd'.txt'" />
			<staticLogFileName value="false" />
			<param name="MaxSizeRollBackups" value="100" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%newline %n记录时间：%date %n线程ID:[%thread] %logger %n -%m%n" />
			</layout>
		</appender>

		<logger name="ExamChangeNotify">
			<level value="INFO" />
			<appender-ref ref="ExamChangeNotifyAppender" />
		</logger>

		<logger name="ReceiveInfo">
			<level value="INFO" />
			<appender-ref ref="ReceiveInfoAppender" />
		</logger>

		<logger name="SendInfo">
			<level value="INFO" />
			<appender-ref ref="SendInfoAppender" />
		</logger>

		<logger name="CancelFeeApp">
			<level value="INFO" />
			<appender-ref ref="CancelFeeAppAppender" />
		</logger>

		<logger name="WebInfo">
			<level value="INFO" />
			<appender-ref ref="WebInfoAppender" />
		</logger>

		<logger name="ErrorLog">
			<level value="ERROR" />
			<appender-ref ref="RollingLogFileAppender" />
		</logger>
		<logger name="YJInfo">
			<level value="INFO" />
			<appender-ref ref="YJInfoAppender" />
		</logger>
	</log4net>
</configuration>