
 
记录时间：2025-06-11 11:07:42,533 
线程ID:[8] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/11 11:07:42

 
记录时间：2025-06-11 11:08:34,479 
线程ID:[8] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo IN ('30601','30602') OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')))

 
记录时间：2025-06-11 11:08:35,333 
线程ID:[8] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>,<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>,
                           <PERSON>.<PERSON>, F.<PERSON>, F.<PERSON>ppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo IN ('30601','30602') OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')))
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo IN ('30601','30602') OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')))

 
记录时间：2025-06-11 11:10:02,140 
线程ID:[8] ExamChangeNotify 
 -查询时间范围: 2025-05-12 00:00:00 到 2025-05-12 23:59:59

 
记录时间：2025-06-11 11:10:13,359 
线程ID:[8] ExamChangeNotify 
 -未找到需要处理的申请单数据

 
记录时间：2025-06-11 11:10:13,359 
线程ID:[8] ExamChangeNotify 
 -申请单状态变更任务结束 2025/6/11 11:10:13

 
记录时间：2025-06-11 11:10:14,813 
线程ID:[8] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/11 11:10:14

 
记录时间：2025-06-11 11:10:16,212 
线程ID:[8] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo IN ('30601','30602') OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')))

 
记录时间：2025-06-11 11:10:16,212 
线程ID:[8] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo IN ('30601','30602') OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')))
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo IN ('30601','30602') OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')))

 
记录时间：2025-06-11 11:10:16,884 
线程ID:[8] ExamChangeNotify 
 -查询时间范围: 2025-05-12 00:00:00 到 2025-05-12 23:59:59

 
记录时间：2025-06-11 11:10:17,660 
线程ID:[8] ExamChangeNotify 
 -未找到需要处理的申请单数据

 
记录时间：2025-06-11 11:10:17,660 
线程ID:[8] ExamChangeNotify 
 -申请单状态变更任务结束 2025/6/11 11:10:17

 
记录时间：2025-06-11 13:05:43,373 
线程ID:[11] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/11 13:05:43
