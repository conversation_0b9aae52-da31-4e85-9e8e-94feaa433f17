@{
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>申请单状态变更调度服务</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="~/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="~/layuiadmin/style/admin.css" media="all">
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md8">
                <div class="layui-card layui-form" lay-filter="component-form-element">
                    <div class="layui-card-header">申请单状态变更服务作业</div>
                    <div class="layui-card-body layui-row layui-col-space10">
                        <div class="layui-col-md12">
                            <label class="layui-form-label">服务状态：</label>
                            <input type="checkbox" lay-filter="job" name="isStart" lay-skin="switch" lay-text="开启|关闭" />
                        </div>
                        <div class="layui-col-md12" style="margin-top: 20px;">
                            <div class="layui-form-item">
                                <label class="layui-form-label">执行时间：</label>
                                <div class="layui-input-block">
                                    <span class="layui-badge layui-bg-blue">每天早晨 5:00 执行</span>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md12" style="margin-top: 20px;">
                            <div class="layui-form-item">
                                <label class="layui-form-label">功能说明：</label>
                                <div class="layui-input-block">
                                    <div class="layui-text">
                                        <p>定时检查申请单状态变更，处理患者退费等状态变化，调用ExamChangeNotify接口释放号源。</p>
                                        <p>可在配置文件中设置执行科室、项目代码、患者来源等过滤条件。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md12" style="margin-top: 20px;">
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-normal" id="executeOnce">立即执行一次（测试）</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/layuiadmin/layui/layui.js"></script>
    <script>
        layui.config({
            base: '../../../layuiadmin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'form'],
            function () {
                var $ = layui.$, form = layui.form, layer = layui.layer;
                
                initilize();
                
                function initilize() {
                    $.ajax({
                        url: "/SendOrder/ExamChangeStatus/IsStart",
                        type: "post",
                        success: function (res) {
                            if (res.code == 0) {
                                form.val("component-form-element",
                                    {
                                        "isStart": true
                                    });
                            }
                        },
                        error: function() {
                            layer.msg('检查服务状态失败');
                        }
                    });
                }
                
                form.on('switch(job)',
                    function (data) {
                        if (data.elem.checked) {
                            start();
                        } else {
                            pause();
                        }
                    });

                function start() {
                    $.ajax({
                        url: "/SendOrder/ExamChangeStatus/Start",
                        type: "post",
                        success: function (res) {
                            layer.msg(res.msg);
                        },
                        error: function() {
                            layer.msg('启动服务失败');
                        }
                    });
                }

                function pause() {
                    $.ajax({
                        url: "/SendOrder/ExamChangeStatus/Pause",
                        type: "post",
                        success: function (res) {
                            layer.msg(res.msg);
                        },
                        error: function() {
                            layer.msg('暂停服务失败');
                        }
                    });
                }
                
                // 立即执行一次按钮事件
                $('#executeOnce').on('click', function() {
                    layer.confirm('确定要立即执行一次申请单状态变更任务吗？', {
                        btn: ['确定', '取消']
                    }, function(index) {
                        $.ajax({
                            url: "/SendOrder/ExamChangeStatus/ExecuteOnce",
                            type: "post",
                            success: function (res) {
                                layer.msg(res.msg);
                            },
                            error: function() {
                                layer.msg('执行任务失败');
                            }
                        });
                        layer.close(index);
                    });
                });
            });
    </script>
</body>
</html>
