{
  "Url": {
    "ClientName": "AngelwinWeb",
    "GetAutoBookedInfo": "http://localhost:8005/BookDetails/BookDetails/GetAutoBookedInfo", //预约平台自动预约接口
    "ExamChangeNotify": "http://localhost:8005/BookDetails/BookDetails/ExamChangeNotify", //预约平台申请单状态接口
    "ExamRequestStatusChanged": "http://**********:80/esb/ekapi/http/hics?signName=ExamAppoint_YJYYPT&from=YJYYPT&formatType=XML&accessToken=", //医技平台接收申请单状态
    "SingInUrl": "http://**********:80/esb/ekapi/http/hics?signName=YjSign&from=YJYYPT&formatType=XML&accessToken=", //预约签到接口，接收到检状态
    "GetPatInfoByEcURL": "http://***********:32000/insur/rti/readPatInfoByEcQuery", //根据医保二维码字符串获取患者信息接口
    "GetExamRequest": "http://**********:80/esb/ekapi/http/hics?signName=GetExamRequest&from=YJYYPT&formatType=XML&accessToken=", //检查申请单查询请求
    "IntegrationUrl": "http://**********/services/HIPMessageServer?wsdl"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Data": {
    "DefaultConnection": {
      "ConnectionString": "Server=***********;Database=ScheduleRouteDemo_JM;UID=sa;PWD=*******;Connection Timeout=6000;", //数据库配置
      //"ConnectionString": "Server=.;Database=ScheduleRoute;UID=sa;PWD=***;Connection Timeout=6000;", //数据库配置
      "HisConnectionString": "Server=**********;Database=goldpacs_new;UID=*****;PWD=*****;Connection Timeout=6000;" //His数据库配置
    }
  },
  "AppSettings": {
    "SendOrderQuartzIsStart": "false", //启动就开启预约消息推送的自动分发服务 true:是|false:否
    "YJ_AutoScheduleQuartzIsStart": "false", //启动就开启申请单分发自动预约服务 true:是|false:否
    "ClientName": "AngelwinWeb", //预约平台第三方调用者名称，在预约平台后台-系统管理-三方调用授权菜单（内部）
    "ClientName_ThirdParty": "HIS", //预约平台第三方调用者名称，在预约平台后台-系统管理-三方调用授权菜单（外部）
    "DistributeTime": "15", //自动预约服务的分发时间 单位：秒
    "SendOrderJob": "15", //消息自动分发服务分发时间 单位：秒
    "IsMessage": "true", //是否发送短信
    //"smsCode": "ExamAppointSuccess", //"c2ac0913512140818d19cbb377066c87", //平台短信服务标识
    //"faultSmsCode": "CheckService", //平台短信设备故障短信ID (弃用)
    //"sendMsgUrl": "http://**********:80/mc/ekapi/sendSmsAuth?accessToken=", //发送短信接口
    //"TelephoneTest": "15720806728", //短信发送 测试手机号
    //"IsWeChat": "false", //是否发送微信
    //"WeChatUrl": "", //微信消息推送地址
    //"ReduceNote": "请在检查当天报到", //精简注意事项
    //"ER_ApplyDepNo": ",301718,301304,301306,301308,301717,301713,301705,3013,301307,301716,301715,301707,301302,301301,301704,301701,301719,301709,301708,301712,301303,301309,301703,301702,301714,301305,301706,301711,301710,3017,", //急诊申请科室代码
    "AutoScheduleTimeBuffer": "120", //, //自动预约缓冲等待时间 单位：秒
    //"PostponeMin": "205922,I,5|205923,I,5|205908,O,20", //自动把患者预约到n分钟之后的时段（项目代码,患者来源,延迟时间|项目代码,患者来源,延迟时间）单位：分钟
    //"PaiduiWaitStoreProcedure": "YJYY_GET_PaiduiWait", //排队等待存储过程名称
    //"KF_ExecutiveDepNo": ",4003,4006,", //空腹患者执行科室代码
    //"KF_AgeLimit": "18", //空腹患者年龄限制 单位：岁
    //"KF_AutoScheduleTimeLimit": "6" //空腹患者预约延迟限制 单位：小时
    "SendMsg": {
      "MsgUrl": "http://10.2.30.109:11221/webservice1.asmx?wsdl",
      "MessageDeptIDs": "", //为空，则走默认所有科室模板一致，否则根据配置的科室走不同的模板,所填内容举例:,10304,11001,
      "TelephoneTest": "",
      "MessageSource": ",O,", //短信限制患者来源
      "AccountId": "10017",
      "Password": "tzyt@***",
      "Type": "TZYT1", //接口类型
      "ManualSmsTemplate": "{患者名称}({性别})，您的{检查项目}请前往{地点}进行预约。",
      "ManualMessageDeptIDs": ",,", //根据配置科室确定是否发送人工预约短信，所填内容举例:,10304,11001,
      "ManualMessageSource": ",O," //人工预约短信患者来源限制
    },
    "IntegrationRequest_TimeOut": "3", //推送医嘱申请信息到集成平台，设置超时时间
    "ResendOrders": {
      "ResendOrdersHisUrl": "http://10.2.30.222:8099/HisWs/GetExamInfo", //补发接收申请His数据接口
      //"ResendBeginDate": "", //补发接收申请开始时间
      //"ResendEndDate": "", //补发接收申请结束时间
      "DateRange": "-1"
    },
    "CS_ExecutiveDepNos": ",1400405,", //超声执行科室代码，所填内容举例:,10304,11001,
    "ExamChangeStatusSchedule": {
      "QuartzIsStart": "true", //启动就开启申请单状态变更调度服务 true:是|false:否
      "ScheduleTime": "0 0 5 * * ?", //调度执行时间0 0 5 * * ? 每天凌晨5点 0 */5 * * * ? 每五分钟
      "DeptProjectRule": ",30601,30602,30302$*7041065$*7041099,",
      //"ExecutiveDepNos": ",30601,", //申请单状态变更执行科室代码，所填内容举例:,10304,11001,
      //"ProjectNos": ",*7041065,", //申请单状态变更项目代码，所填内容举例:,205922,205923,
      "PatientSource": ",O,", //申请单状态变更患者来源，所填内容举例:,I,O,
      "DaysAgo": "30", //待处理数据间隔天数：30天之前
      "StartTime": "2022-06-01 00:00:00", //仅测试环境！！！
      "EndTime": "2022-06-01 23:59:59" //仅测试环境！！！
    }
  },
  "AllowedHosts": "*"
}