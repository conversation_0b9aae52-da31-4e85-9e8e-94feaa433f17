﻿using IntegrationPlatform.Areas.Models;
using IntegrationPlatform.Areas.SendOrder.Models;
using IntegrationPlatform.Common;
using IntegrationPlatform.Common.ApiHelper;
using IntegrationPlatform.Models;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Quartz;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Xml;
using System.Xml.Serialization;
using YJAppoint.Models;
using YJAppoint.Tool;

namespace IntegrationPlatform.Areas.Business
{
    [DisallowConcurrentExecution]
    public class YJ_AutoScheduleJob : IJob
    {
        private readonly IHostingEnvironment _hostingEnvironment;
        public IConfiguration config { get; }

        public YJ_AutoScheduleJob(IServiceProvider serviceProvider,  IHostingEnvironment hostingEnvironment, IConfiguration Configuration)
        {
            _hostingEnvironment = hostingEnvironment;
            config = Configuration;
        }

        public Task Execute(IJobExecutionContext context)
        {
            var st = DateTime.Now;   
            LogHelper.WriteInfo(LogNames.YJInfo.ToString(), $"自动预约作业开始执行{st:yyyy-MM-dd HH:mm:ss.fff}" );
            try
            {
                #region 重新声明一下上下文

                var contextOptionsBuilder = new DbContextOptionsBuilder<YJAppointDbContext>();
                string Constr = config["Data:DefaultConnection:ConnectionString"];
                contextOptionsBuilder.UseSqlServer(Constr);
                var db = new YJAppointDbContext(contextOptionsBuilder.Options);

                #endregion 重新声明一下上下文

                #region 读取配置文件
                
                string MsgUrl = config["AppSettings:SendMsg:MsgUrl"] + "";
                string smsTemplate = config["AppSettings:SendMsg:ManualSmsTemplate"];
                string ManualMessageDeptIDs = config["AppSettings:SendMsg:ManualMessageDeptIDs"];
                string ManualMessageSource = config["AppSettings:SendMsg:ManualMessageSource"];
                string telephoneTest = config["AppSettings:SendMsg:TelephoneTest"];
                string MessageSource = config["AppSettings:SendMsg:MessageSource"] + "";
                string AccountId = config["AppSettings:SendMsg:AccountId"] + "";
                string Password = config["AppSettings:SendMsg:Password"] + "";
                string InterfaceType = config["AppSettings:SendMsg:Type"] + "";
                string GetAutoBookedInfo = config["Url:GetAutoBookedInfo"];  //预约平台后台服务接口(是我们自己的)
                string ExamAddress = config["Url:ExamChangeNotify"];//申请单状态及内容变化接口(是我们自己的)
                string clientName = config["AppSettings:ClientName"];
                string CS_ExecutiveDepNos = config["AppSettings:CS_ExecutiveDepNos"] + "";
                var integrationUrl = config["Url:IntegrationUrl"] + "";
                var clientInfo = db.ClientInfo.FirstOrDefault(a => a.ClientName == clientName);
                var InDate = DateTime.Now.ToString("yyyyMMddHHmmss");
                var myDigest = EncryptionCodingTool.HashSHACode(clientName + InDate + clientInfo.ClientCode);
                var authStr = EncryptionCodingTool.Encrypt3DES(clientInfo.ClientCode + "$" + InDate + "$" + myDigest, clientInfo.PermitKey);
                string token = clientName + "$" + authStr;

                #endregion 读取配置文件

                ScheduleRequestListDto csScheduleRequestListDto = new ScheduleRequestListDto
                {
                    AlyList = new List<ScheduleRequestDto>()
                };
                // 获取上次执行时间
                var lastFireTime = context.PreviousFireTimeUtc?.LocalDateTime;
                var startTime = DateTime.Now;
                //自动预约缓冲等待时间 单位：秒
                var autoScheduleTimeBuffer = !string.IsNullOrWhiteSpace(config["AppSettings:AutoScheduleTimeBuffer"]) ? -Double.Parse(config["AppSettings:AutoScheduleTimeBuffer"]) : -10;
                var TempDate = startTime.AddSeconds(autoScheduleTimeBuffer);

                // 记录详细的执行信息
                LogHelper.WriteInfo(LogNames.YJInfo.ToString(),
                    $"执行参数：当前时间={startTime:yyyy-MM-dd HH:mm:ss.fff}, " +
                    $"缓冲时间={autoScheduleTimeBuffer}秒, " +
                    $"查询时间点={TempDate:yyyy-MM-dd HH:mm:ss.fff}");

                var apply1 = db.ApplyInfo.Where(b => b.DistributeStatus == 0 && b.CreateTime <= TempDate).OrderByDescending(o => o.AID).Take(200).ToList();
                var apply2 = db.ApplyInfo.Where(b => b.DistributeStatus == 0 && b.CreateTime > TempDate).OrderByDescending(a => a.AID).Take(200).ToList();
                
                // 记录查询结果
                LogHelper.WriteInfo(LogNames.YJInfo.ToString(),
                    $"查询结果：缓冲前申请数={apply1.Count}, 缓冲后申请数={apply2.Count}");
                var apply3 = new List<ApplyInfo>();
                foreach (var item in apply1)
                {
                    foreach (var item2 in apply2)
                    {
                        if (item.PatientID == item2.PatientID && (apply3.Where(a => a.DocDetailedNo == item2.DocDetailedNo).Count() == 0))
                        {
                            apply3.Add(item2);
                        }
                    }
                }
                apply3.AddRange(apply1);

                #region 住院床旁超声逻辑处理
                var sst = DateTime.Now;
                LogHelper.WriteInfo(LogNames.YJInfo.ToString(), $"床旁超声逻辑处理开始");
                // 按患者ID和执行科室分组
                var groupedApplies = apply3.Where(d => d.DocOperCode == "NW" && d.PatientSource == "I" && ("," + d.ExecutiveDepNo + ",").Contains(CS_ExecutiveDepNos))
                    .GroupBy(a => new { a.PatientID, a.ExecutiveDepNo })
                    .ToList();

                // 处理每组数据
                foreach (var group in groupedApplies)
                {
                    // 检查这组数据（同一患者同一执行科室）中是否存在住院床旁项目
                    var hasBedsideProject = group.Any(a => a.ProjectName?.Contains("床旁") ?? false);
                    if (hasBedsideProject)
                    {
                        // 将该组所有申请单标记为床边并更新分发状态
                        foreach (var item in group)
                        {
                            // 只查询一次数据库
                            var applyInfo = db.ApplyInfo.FirstOrDefault(a => a.DocDetailedNo == item.DocDetailedNo);
                            if (applyInfo != null)
                            {
                                // 更新分发状态
                                applyInfo.DistributeStatus = 1;
                                applyInfo.DistributeTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                                db.Entry(applyInfo).Property(x => x.DistributeStatus).IsModified = true;
                                db.Entry(applyInfo).Property(x => x.DistributeTime).IsModified = true;

                                // 更新床旁标记
                                if (!applyInfo.OtherBZ?.Contains("床旁") ?? true)
                                {
                                    applyInfo.OtherBZ = string.IsNullOrEmpty(applyInfo.OtherBZ)
                                        ? "床旁"
                                        : applyInfo.OtherBZ + ";床旁";
                                    db.Entry(applyInfo).Property(x => x.OtherBZ).IsModified = true;
                                }
                            }

                            // 从apply3中移除这些项，使其不参与后续的自动预约
                            apply3.Remove(item);
                        }
                        db.SaveChanges();
                    }
                }
                var sse = DateTime.Now;
                LogHelper.WriteInfo(LogNames.YJInfo.ToString(), $"床旁超声逻辑处理结束：耗时={(sse - sst).TotalSeconds:F3}秒");
                #endregion

                if (apply3 != null && apply3.ToList().Count > 0)
                {
                    var apply = apply3.OrderBy(a => a.PatientID).ThenBy(a => a.ExecutiveDepNo).ToList();
                    for (int j = 0; j < apply.Count; j++)
                    { 
                        //更新分发状态
                        UpdateDistributionState(apply[j].DocDetailedNo, 1);

                        if (apply[j].DocOperCode == "CA")
                        {
                            #region 退费时非床旁发送取消预约消息
                            if (!(apply[j].OtherBZ?.Contains("床旁") ?? false && apply[j].PatientSource == "I" && ("," + apply[j].ExecutiveDepNo + ",").Contains(CS_ExecutiveDepNos)))
                            {
                                //退费时非床旁发送取消预约消息
                                SendOrderCanceled(integrationUrl, apply[j], db);
                            }
                            #endregion

                            #region CA 调用退费
                            try
                            {
                                #region 患者退费调用ExamChangeNotify接口

                                ExamChangeNotify examChange = new ExamChangeNotify();
                                examChange.DocDetailedNo = apply[j].DocDetailedNo;
                                examChange.PatientId = apply[j].PatientID;
                                examChange.StudyStatus = "10";
                                string applyInfoJson = JsonConvert.SerializeObject(examChange);
                                LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "患者退费,调用ExamChangeNotify释放号源接口，入参：" + applyInfoJson);
                                var TFResult = RequestApi.SystrohHttpPostByToken(ExamAddress, applyInfoJson, token);
                                LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "患者退费,调用ExamChangeNotify释放号源接口，接口返回值：" + TFResult);

                                #endregion 患者退费调用ExamChangeNotify接口
                            }
                            catch (Exception e)
                            {
                                LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "患者退费,调用ExamChangeNotify释放号源接口【异常】：" + e);
                                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                            }
                            #endregion CA 调用退费
                        }
                        else
                        {
                            #region 超声住院OtherBZ为床旁的不走自动预约
                            if (apply[j].OtherBZ?.Contains("床旁") ?? false && apply[j].PatientSource == "I" && ("," + apply[j].ExecutiveDepNo + ",").Contains(CS_ExecutiveDepNos))
                            {
                                continue;
                            }
                            #endregion

                            //#region 超声住院OtherBZ不为空的走人工预约
                            //if (!string.IsNullOrEmpty(apply[j].OtherBZ) && apply[j].PatientSource == "I" && ("," + apply[j].ExecutiveDepNo + ",").Contains(CS_ExecutiveDepNos))
                            //{
                            //    continue;
                            //}
                            //#endregion

                            #region 退费重收避免已经拆单的再次自动预约合单
                            var data = db.ApplyAfterDealInfo.Where(d => d.AppFormNo == apply[j].DocDetailedNo || d.AppFormNo == apply[j].CombinedCode).FirstOrDefault();
                            if (data != null)
                            {
                                continue;
                            }
                            #endregion

                            if (j != apply.Count - 1 && apply[j].PatientID == apply[j + 1].PatientID && apply[j].ExecutiveDepNo == apply[j + 1].ExecutiveDepNo)
                            {
                                ScheduleRequestDto scheduleRequestDto = new ScheduleRequestDto
                                {
                                    PatientID = apply[j].PatientID,
                                    DocDetailedNo = apply[j].DocDetailedNo,
                                    StudyNo = apply[j].StudyNo,
                                    ProjectNo = apply[j].ProjectNo,
                                    ProjectName = apply[j].ProjectName,
                                    YJAccessionNo = apply[j].YJAccessionNo,
                                    Combinedstate = "1",
                                    OrderUser = "Distribution",
                                    DocOperCode = apply[j].DocOperCode,
                                    HospitalAreaId = apply[j].HospitalAreaCode,
                                    DepCode = apply[j].ExecutiveDepNo,
                                    if_kf = apply[j].AppendMessage
                                };
                                csScheduleRequestListDto.AlyList.Add(scheduleRequestDto);
                            }
                            else
                            {
                                ScheduleRequestDto scheduleRequestDto = new ScheduleRequestDto
                                {
                                    PatientID = apply[j].PatientID,
                                    DocDetailedNo = apply[j].DocDetailedNo,
                                    StudyNo = apply[j].StudyNo,
                                    ProjectNo = apply[j].ProjectNo,
                                    ProjectName = apply[j].ProjectName,
                                    YJAccessionNo = apply[j].YJAccessionNo,
                                    Combinedstate = "1",
                                    OrderUser = "Distribution",
                                    DocOperCode = apply[j].DocOperCode,
                                    HospitalAreaId = apply[j].HospitalAreaCode,
                                    DepCode = apply[j].ExecutiveDepNo,
                                    if_kf = apply[j].AppendMessage
                                };
                                csScheduleRequestListDto.AlyList.Add(scheduleRequestDto);

                                try
                                {
                                    var jsonBody = JsonConvert.SerializeObject(csScheduleRequestListDto);
                                    LogHelper.WriteInfo(LogNames.YJInfo.ToString(), "自动预约接口入参：" + jsonBody);
                                    string Result = RequestApi.SystrohHttpPostByToken(GetAutoBookedInfo, jsonBody, token);
                                    LogHelper.WriteInfo(LogNames.YJInfo.ToString(), "自动预约接口返回：" + Result);
                                    if (csScheduleRequestListDto != null && csScheduleRequestListDto.AlyList.Count() > 0)
                                    {
                                        var resModel = JsonConvert.DeserializeObject<ScheduleResponseDto>(Result);
                                        if (resModel.ResultCode == "0" && resModel.AutoBookedResultList != null && resModel.AutoBookedResultList.Count > 0)
                                        {
                                            var docDetailedNos = resModel.AutoBookedResultList.Select(d => d.DocDetailedNo).ToList();
                                            List<ApplyInfo> applyInfo = db.ApplyInfo.Include(e => e.PatientInfo).Include(e => e.ChargeCompare).Where(b => docDetailedNos.Contains(b.DocDetailedNo) && !(string.IsNullOrEmpty(b.ProjectNo))).ToList();

                                            foreach (var item in resModel.AutoBookedResultList)
                                            {
                                                if (item.ResultCode != "0")
                                                {
                                                    SendSMSNew(smsTemplate, telephoneTest, MsgUrl, AccountId, Password,item.DocDetailedNo, applyInfo, ManualMessageDeptIDs, ManualMessageSource);
                                                }
                                            }
                                        }
                                    }
                                    csScheduleRequestListDto.AlyList.Clear();
                                }
                                catch (Exception e)
                                {
                                    LogHelper.WriteInfo(LogNames.YJInfo.ToString(), "超声预约失败了：" + e.ToString());
                                    LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                                    csScheduleRequestListDto.AlyList.Clear();

                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }
            var endTime = DateTime.Now;
            LogHelper.WriteInfo(LogNames.YJInfo.ToString(),
                $"自动预约作业结束{endTime}，耗时：{(endTime - st).TotalSeconds:F3}秒");

            return Task.CompletedTask;
        }

        public void UpdateDistributionState(string docDetailNo, int state)
        {
            var contextOptionsBuilder = new DbContextOptionsBuilder<YJAppointDbContext>();
            contextOptionsBuilder.UseSqlServer(config["Data:DefaultConnection:ConnectionString"]);
            var db = new YJAppointDbContext(contextOptionsBuilder.Options);

            var entity = db.ApplyInfo.FirstOrDefault(a => a.DocDetailedNo == docDetailNo);
            if (entity != null)
            {
                entity.DistributeStatus = state;
                entity.DistributeTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                db.SaveChanges();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="db"></param>
        /// <param name="docDetailedNo"></param>
        /// <returns></returns>
        public void SendSMS(string smsTemplate, string telephoneTest, string MsgUrl, string AccountId, string Password, string InterfaceType, string docDetailedNo, List<ApplyInfo> applyInfos, string ManualMessageDeptIDs)
        {
            var applyInfo = applyInfos.Where(d => d.DocDetailedNo == docDetailedNo).FirstOrDefault();
            if (ManualMessageDeptIDs.Contains("," + applyInfo.ExecutiveDepNo + ","))
            {
                //for (int i = 0; i < applyInfos.Count; i++)
                //{
                //患者信息                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
                PatientInfo patient = applyInfo.PatientInfo;
                var chargeCompare = applyInfo.ChargeCompare;
                string SendMsg = smsTemplate.Replace("{患者名称}", patient.PatientName).Replace("{性别}", patient.Sex)
                                    .Replace("{检查项目}", applyInfo.ProjectName)
                                    .Replace("{地点}", string.IsNullOrEmpty(chargeCompare.Msg) ? "一层服务台" : chargeCompare.Msg);
                var requestDto = new BookedInfoDTO
                {
                    SendType = "ShortMsg",
                    Telephone = string.IsNullOrEmpty(patient.TelephoneNew) ? patient.Telephone : patient.TelephoneNew,
                    Msg = SendMsg
                };
                if (!string.IsNullOrEmpty(telephoneTest))
                {
                    requestDto.Telephone = telephoneTest;
                }
                string msgBody = $"<root><row><TransactionId>Short{Guid.NewGuid()}</TransactionId><Phones>{requestDto.Telephone}</Phones><SendMassage>{requestDto.Msg}</SendMassage><ChannelFlag>TZYT</ChannelFlag></row></root>";

                try
                {
                    LogHelper.WriteInfo(LogNames.YJInfo.ToString(), $"短信消息推送入参-MsgUrl:{MsgUrl}\r\n AccountId:{AccountId}\r\n InterfaceType:{InterfaceType}\r\n msgBody：{msgBody}");
                    string result = CallMessageByXML(MsgUrl, AccountId, Password, InterfaceType, msgBody);
                    LogHelper.WriteInfo(LogNames.YJInfo.ToString(), "短信消息推送出参：" + result);
                }
                catch (Exception exMsg)
                {
                    LogHelper.WriteInfo(LogNames.YJInfo.ToString(), "短信发送失败：" + exMsg);
                    LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), exMsg);
                }
                //}
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="Url"></param>
        /// <param name="action"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public string CallMessageByXML(string msgUrl, string accountId, string password, string interfaceType, string msgBody)
        {
            //try
            //{
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("p1", accountId);
            parameters.Add("p2", password);
            parameters.Add("p3", interfaceType);
            parameters.Add("p4", $"<![CDATA[{msgBody}]]>");
            string xml = $@"<soap:Envelope xmlns:soap=""http://www.w3.org/2003/05/soap-envelope"" xmlns:tem=""http://tempuri.org/"">
                            <soap:Header/>
                            <soap:Body>
                                <tem:Web_ChisRequireData>
                                    <!--Optional:-->
                                    <tem:userName>{parameters["p1"]}</tem:userName>
                                    <!--Optional:-->
                                    <tem:passWord>{parameters["p2"]}</tem:passWord>
                                    <!--Optional:-->
                                    <tem:businessType>{parameters["p3"]}</tem:businessType>
                                    <!--Optional:-->
                                    <tem:params3>{parameters["p4"]}</tem:params3>
                                </tem:Web_ChisRequireData>
                            </soap:Body>
                        </soap:Envelope>";

            WebRequest webRequest = WebRequest.Create(msgUrl);
            webRequest.ContentType = $"application/soap+xml; charset=UTF-8";
            webRequest.Method = "POST";
            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "发送短信【CallMessageByXML】request：" + xml);
            using (Stream requestStream = webRequest.GetRequestStream())
            {
                byte[] paramBytes = Encoding.UTF8.GetBytes(xml);
                requestStream.Write(paramBytes, 0, paramBytes.Length);
            }

            //响应
            WebResponse webResponse = webRequest.GetResponse();
            string resp = JsonConvert.SerializeObject(webResponse);
            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "发送短信【CallMessageByXML】response：" + resp);
            using (StreamReader myStreamReader = new StreamReader(webResponse.GetResponseStream(), Encoding.UTF8))
            {
                return myStreamReader.ReadToEnd();
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="db"></param>
        /// <param name="docDetailedNo"></param>
        /// <returns></returns>
        public void SendSMSNew(string smsTemplate, string telephoneTest, string MsgUrl, string AccountId, string Password, string docDetailedNo, List<ApplyInfo> applyInfos, string ManualMessageDeptIDs,string ManualMessageSource)
        {
            var applyInfo = applyInfos.Where(d => d.DocDetailedNo == docDetailedNo).FirstOrDefault();
            if (ManualMessageDeptIDs.Contains("," + applyInfo.ExecutiveDepNo + ",") && ManualMessageSource.Contains("," + applyInfo.PatientSource + ","))
            {
                //患者信息                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
                PatientInfo patient = applyInfo.PatientInfo;
                var chargeCompare = applyInfo.ChargeCompare;
                string SendMsg = smsTemplate.Replace("{患者名称}", patient.PatientName).Replace("{性别}", patient.Sex)
                                    .Replace("{检查项目}", applyInfo.ProjectName)
                                    .Replace("{地点}", string.IsNullOrEmpty(chargeCompare.Msg) ? "一层服务台" : chargeCompare.Msg);
                string tel = string.IsNullOrEmpty(patient.TelephoneNew) ? patient.Telephone : patient.TelephoneNew;
                if (!string.IsNullOrEmpty(telephoneTest))
                {
                    tel = telephoneTest;
                }
                //string msgBody = $"<request><apName>{AccountId}</apName><apPassword>{Password}</apPassword><srcId></srcId>" +
                //                $"<serviceId><orgMsgId>*********</orgMsgId><receiptUrl></receiptUrl></serviceId>" +
                //                $"<calledNumber>{tel}</calledNumber>" +
                //                $"<content><![CDATA[{HttpUtility.UrlEncode(SendMsg)}]]></content><sendTime></sendTime></request>";
                try
                {
                    LogHelper.WriteInfo(LogNames.YJInfo.ToString(), $"非自动预约短信消息推送入参-MsgUrl:{MsgUrl}\r\n AccountId:{AccountId}\r\n msgBody：{SendMsg}");
                    var client = new RestClient(MsgUrl);
                    client.Timeout = -1;
                    var request = new RestRequest(Method.POST);
                    //request.AddHeader("Content-Type", "text/plain");
                    //request.AddParameter("text/plain", msgBody, ParameterType.RequestBody);
                    request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
                    request.AddParameter("apName", AccountId);
                    request.AddParameter("apPassword", Password);
                    request.AddParameter("calledNumber", tel);
                    request.AddParameter("content", SendMsg);
                    request.AddParameter("srcId", "");
                    request.AddParameter("serviceId", "{\"orgMsgId\":\"123456\"}");
                    request.AddParameter("sendTime", "");
                    IRestResponse response = client.Execute(request);
                    string result = response.Content;
                    LogHelper.WriteInfo(LogNames.YJInfo.ToString(), "非自动预约短信消息推送出参：" + result);
                }
                catch (Exception exMsg)
                {
                    LogHelper.WriteInfo(LogNames.YJInfo.ToString(), "非自动预约短信发送失败：" + exMsg);
                    LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), exMsg);
                }
            }
        }


        public void SendOrderCanceled(string integrationUrl, ApplyInfo applyInfo, YJAppointDbContext db)
        {

            #region 1.调用医技预约消息取消推送接口
            if (applyInfo.DocOperCode != "CA")
            {
                return;
            }
            ApplyAfterDealInfo afterApplyInfo = null;
            string mainDocDetailNo = applyInfo.DocDetailedNo;
            if (!string.IsNullOrEmpty(applyInfo.CombinedCode) && !applyInfo.CombinedCode.Equals(applyInfo.DocDetailedNo))
            {
                //子单
                afterApplyInfo = db.ApplyAfterDealInfo.Where(d => d.AppFormNo.Equals(applyInfo.CombinedCode)).FirstOrDefault();
                mainDocDetailNo = applyInfo.CombinedCode;
            }
            else
            {
                afterApplyInfo = db.ApplyAfterDealInfo.Where(d => d.AppFormNo.Equals(applyInfo.DocDetailedNo)).FirstOrDefault();
            }

            LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), $"【接收医嘱退费】推送取消预约开始,处置状态:{afterApplyInfo?.StudyStatus},医嘱号：{applyInfo.DocDetailedNo}");
            if (afterApplyInfo?.StudyStatus == "0")
            {
                var SOMsg = db.SendOutMessage.Where(d => d.SendMegsType.Equals("预约") && d.DocDetailedNo == mainDocDetailNo).FirstOrDefault();
                if (SOMsg != null && !string.IsNullOrEmpty(SOMsg?.ReMegs))
                {
                    LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "获取sendOutMsg预约数据：" + mainDocDetailNo);
                    ////检查信息模型
                    var bookedReMsg = XmlUtil.XmlDeserialize<BookedInfoReMsg>(SOMsg?.ReMegs, Encoding.UTF8);
                    //获取推送消息模板内容
                    var patientInfo = db.PatientInfo.FirstOrDefault(d => d.PatientID == applyInfo.PatientID);
                    var message = GetCancelMessage(mainDocDetailNo, bookedReMsg, applyInfo, patientInfo, 1, db);//edit by lwc 20250417
                    LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "【接收医嘱退费】推送取消预约接口请求xml：" + message);
                    //Task<string> resu = CallByXMLAsync(integrationUrl, "CheckAppointStatusInfoUpdate", message);
                    string resu = CallByXML(integrationUrl, "CheckAppointStatusInfoUpdate", message);
                    var resultCode = ResolutionXml(resu);
                    if (resultCode == "AA")
                    {
                        LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), $"【接收医嘱退费】推送取消预约成功{applyInfo.DocDetailedNo}");
                    }
                }
            }
            #endregion
        }

        #region 退费推送取消预约消息逻辑

        /// <summary>
        /// 获取推送信息内容
        /// </summary>
        /// <param name="docDetailedNoStr"></param>
        /// <param name="bookedReMsg"></param>
        /// <param name="applyInfo"></param>
        /// <param name="patientInfo"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public string GetCancelMessage(string docDetailedNoStr, BookedInfoReMsg bookedReMsg, ApplyInfo applyInfo, PatientInfo patientInfo, int sendMesCount, YJAppointDbContext db)
        {
            var message = "";
            string scheduleTime = string.Empty;
            try
            {
                XmlDocument xd = new XmlDocument();
                string contentRootPath = _hostingEnvironment.ContentRootPath + "\\wwwroot" + "\\" + "XML模板" + "\\" + "BS004_检查预约状态新增信息模板.xml";
                xd.Load(contentRootPath);
                message = xd.InnerXml;//读取xml文件内容

                #region 赋值相关字段
                string msgTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                string patientSource = applyInfo.PatientSource;
                string patiName = GetSourceName(patientSource);
                string patiArea = patientInfo.InpatientArea?.Replace(patientInfo.Department, "");
                string patiAreaNo = patientInfo.InpatientAreaNo?.Replace(patientInfo.DepartmentNo, "");
                var afterDealInfo = applyInfo.ApplyAfterDealInfos.Where(d => d.AppFormNo == docDetailedNoStr).FirstOrDefault();
                var UserInfo = db.UserInfos.FromSql("SELECT * FROM UserInfos where RaleName = '" + bookedReMsg?.ScheduleUser + "'").FirstOrDefault();

                #endregion

                #region 更新xml内容
                if (sendMesCount > 0)
                {
                    message = message.Replace("PRSC_IN010101UV01", "PRSC_IN010201UV01");
                }

                message = message.Replace("BS004_SerialNumber", Guid.NewGuid().ToString());//消息流水号
                message = message.Replace("BS004_CreationTime", msgTime);
                message = message.Replace("BS004_Number", applyInfo.AID.ToString()); //预约单流水号edit by lwc 20250401
                message = message.Replace("BS004_PatientSource", patientSource);
                message = message.Replace("BS004_PatiName", patiName);
                message = message.Replace("BS004_ScheduleTime", "");//取消预约置空
                message = message.Replace("BS004_AreaID", "01");
                message = message.Replace("BS004_PatientID", applyInfo.PatientID);
                message = message.Replace("BS004_PatientFlag", patientInfo.BLH);//门诊号标识/病人卡号
                message = message.Replace("BS004_HospFlag", patientInfo.BLH);//住院号标识
                message = message.Replace("BS004_VisitNo", applyInfo.DoctorTimes);//就诊次数
                message = message.Replace("BS004_RegisterID", applyInfo.RegisterID);//就诊流水号
                message = message.Replace("BS004_SortID", bookedReMsg?.ScheduleNumber);
                message = message.Replace("BS004_PatientName", patientInfo.PatientName);
                message = message.Replace("BS004_ApplyDepNo", applyInfo.ApplyDepNo);
                message = message.Replace("BS004_ApplyDep", applyInfo.ApplyDep);
                message = message.Replace("BS004_EquipmentNo", bookedReMsg?.EquipmentNo);
                message = message.Replace("BS004_EquipmentName", bookedReMsg?.ExamRoomName);
                message = message.Replace("BS004_ScheduleUserNo", UserInfo?.WorkId);//预约员编码
                message = message.Replace("BS004_ScheduleUser", bookedReMsg?.ScheduleUser);
                message = message.Replace("BS004_ExecutiveDepNo", bookedReMsg?.ExecutiveDepNo);
                message = message.Replace("BS004_ExecutiveDepName", bookedReMsg?.ExecutiveDepName);
                //DONE add by lwc 20250417
                if (patientSource.ToUpper() == "I")
                {
                    message = message.Replace("BS004_DocGroupNo", applyInfo.DocDetailedNo);
                    message = message.Replace("BS004_DocDetailedNo", applyInfo.DocGroupNo);
                }
                else
                {
                    //addbylwc20250424
                    message = message.Replace("BS004_DocGroupNo", string.IsNullOrEmpty(applyInfo.DocGroupNo) ? docDetailedNoStr : applyInfo.DocGroupNo);//申请单编号edit by lwc 20250424
                    message = message.Replace("BS004_DocDetailedNo", applyInfo.DocDetailedNo);//医嘱号 edit by lwc 20250424  
                }
                message = message.Replace("BS004_PatientSex", bookedReMsg?.Sex);
                message = message.Replace("BS004_PatientAge", bookedReMsg?.Age);
                message = message.Replace("BS004_PatientTel", string.IsNullOrEmpty(patientInfo.Telephone) ? patientInfo.TelephoneNew : patientInfo.Telephone);
                message = message.Replace("BS004_Address", patientInfo?.Adress);
                message = message.Replace("BS004_IDCard", bookedReMsg?.SFZH);
                message = message.Replace("BS004_DocMarkName", applyInfo.DocMarkName);
                message = message.Replace("BS004_DocSchedeleDate", applyInfo.DocSchedeleDate);
                message = message.Replace("BS004_ClinicalDiagnosis", applyInfo.AplicationForm);
                message = message.Replace("BS004_ProjectNo", applyInfo.ProjectNo);
                message = message.Replace("BS004_ProjectName", applyInfo.ProjectName);
                message = message.Replace("BS004_ProjectPrice", applyInfo.ProjectPrice);
                message = message.Replace("BS004_InpatiAreaName", patiArea);
                message = message.Replace("BS004_InpatiAreaNo", patiAreaNo);
                message = message.Replace("BS004_OtherBZ", applyInfo.OtherBZ);
                #endregion
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), docDetailedNoStr + "推送信息内容模板获取失败：" + ex.ToString());
            }

            return message;
        }

        /// <summary>
        /// 获取消息推送结果
        /// </summary>
        /// <param name="Url"></param>
        /// <param name="action"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public string CallByXML(string Url, string action, string message)
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("p1", action);
            parameters.Add("p2", $"<![CDATA[{message}]]>");
            string xml = $@"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:urn=""urn:hl7-org:v3"">
                               <soapenv:Header/>
                               <soapenv:Body>
                                  <urn:HIPMessageServer>
                                     <urn:action>{parameters["p1"]}</urn:action>
                                     <urn:message>{parameters["p2"]}</urn:message>
                                  </urn:HIPMessageServer>
                               </soapenv:Body>
                            </soapenv:Envelope>";

            WebRequest webRequest = WebRequest.Create(Url);
            webRequest.ContentType = $"text/xml; charset=UTF-8";//application/soap+xml
            webRequest.Method = "POST";
            webRequest.Headers.Add("SOAPAction", "urn:HIPMessageServer");
            LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "集成平台的BS004【CallByXML】request：" + xml);
            using (Stream requestStream = webRequest.GetRequestStream())
            {
                byte[] paramBytes = Encoding.UTF8.GetBytes(xml);
                requestStream.Write(paramBytes, 0, paramBytes.Length);
            }

            //响应
            WebResponse webResponse = webRequest.GetResponse();
            string resp = JsonConvert.SerializeObject(webResponse);
            //LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "集成平台的BS004【CallByXML】response：" + resp);
            using (StreamReader myStreamReader = new StreamReader(webResponse.GetResponseStream(), Encoding.UTF8))
            {
                return myStreamReader.ReadToEnd();
            }
        }

        /// <summary>
        /// 获取消息推送结果
        /// </summary>
        /// <param name="Url"></param>
        /// <param name="action"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public async Task<string> CallByXMLAsync(string Url, string action, string message)
        {
            string result = string.Empty;
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("p1", action);
            parameters.Add("p2", $"<![CDATA[{message}]]>");
            string xml = $@"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:urn=""urn:hl7-org:v3"">
                               <soapenv:Header/>
                               <soapenv:Body>
                                  <urn:HIPMessageServer>
                                     <urn:action>{parameters["p1"]}</urn:action>
                                     <urn:message>{parameters["p2"]}</urn:message>
                                  </urn:HIPMessageServer>
                               </soapenv:Body>
                            </soapenv:Envelope>";

            WebRequest webRequest = WebRequest.Create(Url);
            webRequest.ContentType = $"text/xml; charset=UTF-8";//application/soap+xml
            webRequest.Method = "POST";
            webRequest.Headers.Add("SOAPAction", "urn:HIPMessageServer");
            LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "集成平台的BS004【CallByXML】request：" + xml);
            using (Stream requestStream = await webRequest.GetRequestStreamAsync())
            {
                byte[] paramBytes = Encoding.UTF8.GetBytes(xml);
                await requestStream.WriteAsync(paramBytes, 0, paramBytes.Length);
            }

            //响应
            WebResponse webResponse = await webRequest.GetResponseAsync();
            string resp = JsonConvert.SerializeObject(webResponse);
            //LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "集成平台的BS004【CallByXML】response：" + resp);
            using (StreamReader myStreamReader = new StreamReader(webResponse.GetResponseStream(), Encoding.UTF8))
            {
                result = await myStreamReader.ReadToEndAsync();
                // 记录响应日志
                await Task.Run(() => LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "集成平台的BS004【CallByXML】response：" + result));
            }
            return result;
        }

        /// <summary>
        /// 解析SOAP格式返回的xml
        /// </summary>
        /// <param name="strxml"></param>
        /// <returns></returns>
        public string ResolutionXml(string strxml)
        {
            #region test
            //            strxml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
            //<soap:Envelope xmlns:soap=""http://www.w3.org/2003/05/soap-envelope"" xmlns:types=""urn:hl7-org:v3"">
            //	<soap:Body>
            //		<types:HIPMessageServerResponse>
            //			<types:payload>
            //				&lt;MCCI_IN000002UV01 xmlns=""urn:hl7-org:v3"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" ITSVersion=""XML_1.0"" xsi:schemaLocation=""urn:hl7-org:v3 ../multicacheschemas/MCCI_IN000002UV01.xsd""&gt;
            //				&lt;id extension=""2c321fcf-04d7-4f8e-beca-6a46254ccc77"" root=""2.16.156.10011.*******""/&gt;
            //				&lt;creationTime value=""20240809104748""/&gt;
            //				&lt;interactionId extension=""MCCI_IN000002UV01"" root=""2.16.156.10011.*******""/&gt;
            //				&lt;processingCode code=""P""/&gt;
            //				&lt;processingModeCode/&gt;
            //				&lt;acceptAckCode code=""AL""/&gt;
            //				&lt;receiver typeCode=""RCV""&gt;
            //				&lt;device classCode=""DEV"" determinerCode=""INSTANCE""&gt;
            //				&lt;id&gt;
            //				&lt;item extension=""@222"" root=""2.16.156.10011.*******""/&gt;
            //				&lt;/id&gt;
            //				&lt;/device&gt;
            //				&lt;/receiver&gt;
            //				&lt;sender typeCode=""SND""&gt;
            //				&lt;device classCode=""DEV"" determinerCode=""INSTANCE""&gt;
            //				&lt;id&gt;
            //				&lt;item extension=""ESB"" root=""2.16.156.10011.*******""/&gt;
            //				&lt;/id&gt;
            //				&lt;/device&gt;
            //				&lt;/sender&gt;
            //				&lt;acknowledgement typeCode=""AA""&gt;
            //				&lt;!--请求消息ID--&gt;
            //				&lt;targetMessage&gt;
            //				&lt;id extension=""@BS004"" root=""2.16.156.10011.*******""/&gt;
            //				&lt;/targetMessage&gt;
            //				&lt;acknowledgementDetail&gt;
            //				&lt;text value=""接收成功""/&gt;
            //				&lt;/acknowledgementDetail&gt;
            //				&lt;/acknowledgement&gt;
            //				&lt;/MCCI_IN000002UV01&gt;
            //			</types:payload>
            //		</types:HIPMessageServerResponse>
            //	</soap:Body>
            //</soap:Envelope>";
            #endregion
            string textCode = "";
            string textContes = "";
            try
            {
                // 使用 XmlSerializer 反序列化 XML 文档
                XmlSerializer xmlFormat = new XmlSerializer(typeof(Envelope));
                using (StringReader reader = new StringReader(strxml))
                {
                    Envelope envelope = (Envelope)xmlFormat.Deserialize(reader);
                    string payload = envelope.Body.HIPMessageServerResponse.Payload;
                    LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"SOAP Payload: {payload}");
                    McciIn000002UV01 payloadModel = ParsePayload(payload);
                    textCode = payloadModel.Acknowledgement.TypeCode.ToString();
                    textContes = payloadModel.Acknowledgement.AcknowledgementDetail.Text.Value;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), "解析SOAP格式返回的xml失败：" + ex.ToString());
            }
            return textCode;
        }

        // 解析 Payload 字符串
        private McciIn000002UV01 ParsePayload(string payload)
        {
            XmlSerializer serializer = new XmlSerializer(typeof(McciIn000002UV01));
            using (StringReader reader = new StringReader(payload))
            {
                return (McciIn000002UV01)serializer.Deserialize(reader);
            }
        }

        private string GetSourceName(string source)
        {
            string result = string.Empty;
            switch (source)
            {
                case "I":
                    result = "住院";//住院
                    break;
                case "O":
                    result = "门诊";//门诊
                    break;
                case "E":
                    result = "急诊"; //急诊 现在划到门诊
                    break;
                case "P":
                    result = "体检"; //普通体检
                    break;
                default:
                    result = "";
                    break;
            };
            return result;
        }
        #endregion
    }
}