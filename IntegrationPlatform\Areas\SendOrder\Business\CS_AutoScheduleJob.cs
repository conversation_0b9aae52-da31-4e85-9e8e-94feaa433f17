using IntegrationPlatform.Areas.Models;
using IntegrationPlatform.Common;
using IntegrationPlatform.Common.ApiHelper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using YJAppoint.Models;
using YJAppoint.Tool;

namespace IntegrationPlatform.Areas.Business
{
    [DisallowConcurrentExecution]
    public class CS_AutoScheduleJob : IJob
    {
        private readonly YJAppointDbContext db2;
        private readonly IHostingEnvironment _hostingEnvironment;
        public IConfiguration config { get; }

        public CS_AutoScheduleJob(IServiceProvider serviceProvider,  IHostingEnvironment hostingEnvironment, IConfiguration Configuration)
        {
            _hostingEnvironment = hostingEnvironment;
            config = Configuration;
        }

        public Task Execute(IJobExecutionContext context)
        {
            LogHelper.WriteInfo(LogNames.CSInfo.ToString(), "自动预约任务开始执行 " + DateTime.Now);
            try
            {
                #region 重新声明一下上下文

                var contextOptionsBuilder = new DbContextOptionsBuilder<YJAppointDbContext>();
                string Constr = config["Data:DefaultConnection:ConnectionString"];
                contextOptionsBuilder.UseSqlServer(Constr);
                var db = new YJAppointDbContext(contextOptionsBuilder.Options);

                #endregion 重新声明一下上下文

                #region 读取配置文件

                string GetAutoBookedInfo = config["Url:GetAutoBookedInfo"];  //预约平台后台服务接口(是我们自己的)
                string ExamAddress = config["Url:ExamChangeNotify"];//申请单状态及内容变化接口(是我们自己的)
                string clientName = config["AppSettings:ClientName"];
                var clientInfo = db.ClientInfo.FirstOrDefault(a => a.ClientName == clientName);
                var InDate = DateTime.Now.ToString("yyyyMMddHHmmss");
                var myDigest = EncryptionCodingTool.HashSHACode(clientName + InDate + clientInfo.ClientCode);
                var authStr = EncryptionCodingTool.Encrypt3DES(clientInfo.ClientCode + "$" + InDate + "$" + myDigest, clientInfo.PermitKey);
                string token = clientName + "$" + authStr;

                #endregion 读取配置文件

                ScheduleRequestListDto csScheduleRequestListDto = new ScheduleRequestListDto
                {
                    AlyList = new List<ScheduleRequestDto>()
                };
                //自动预约缓冲等待时间 单位：秒
                var autoScheduleTimeBuffer = !string.IsNullOrWhiteSpace(config["AppSettings:AutoScheduleTimeBuffer"]) ? -Double.Parse(config["AppSettings:AutoScheduleTimeBuffer"]) : -10;
                var TempDate = DateTime.Now.AddSeconds(autoScheduleTimeBuffer);
                //OtherBZ 表示是否包含床边项目
                var apply1 = db.ApplyInfo.Where(b => b.DistributeStatus == 0 && b.CreateTime <= TempDate).OrderByDescending(o => o.AID).Take(200).ToList();
                var apply2 = db.ApplyInfo.Where(b => b.DistributeStatus == 0 && b.CreateTime > TempDate).OrderByDescending(a => a.AID).Take(200).ToList();
                var apply3 = new List<ApplyInfo>();
                foreach (var item in apply1)
                {
                    foreach (var item2 in apply2)
                    {
                        if (item.PatientID == item2.PatientID && (apply3.Where(a => a.DocDetailedNo == item2.DocDetailedNo).Count() == 0))
                        {
                            apply3.Add(item2);
                        }
                    }
                }
                apply3.AddRange(apply1);

                if (apply3 != null && apply3.ToList().Count > 0)
                {
                    var apply = apply3.OrderBy(a => a.PatientID).OrderBy(a => a.ExecutiveDepNo).ToList();
                    for (int j = 0; j < apply.Count; j++)
                    {
                        //更新分发状态
                        UpdateDistributionState(apply[j].DocDetailedNo, 1);

                        #region CA 调用退费

                        if (apply[j].DocOperCode == "CA")
                        {
                            try
                            {
                                #region 患者退费调用ExamChangeNotify接口

                                ExamChangeNotify examChange = new ExamChangeNotify();
                                examChange.DocDetailedNo = apply[j].DocDetailedNo;
                                examChange.PatientId = apply[j].PatientID;
                                examChange.StudyStatus = "10";
                                string applyInfoJson = JsonConvert.SerializeObject(examChange);
                                LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "患者退费,调用ExamChangeNotify释放号源接口，入参：" + applyInfoJson);
                                var TFResult = RequestApi.SystrohHttpPostByToken(ExamAddress, applyInfoJson, token);
                                LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "患者退费,调用ExamChangeNotify释放号源接口，接口返回值：" + TFResult);

                                #endregion 患者退费调用ExamChangeNotify接口
                            }
                            catch (Exception e)
                            {
                                LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "患者退费,调用ExamChangeNotify释放号源接口【异常】：" + e);
                                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                            }
                        }

                        #endregion CA 调用退费

                        else
                        {
                            if (j != apply.Count - 1 && apply[j].PatientID == apply[j + 1].PatientID && apply[j].ExecutiveDepNo == apply[j + 1].ExecutiveDepNo)
                            {
                                ScheduleRequestDto scheduleRequestDto = new ScheduleRequestDto
                                {
                                    PatientID = apply[j].PatientID,
                                    DocDetailedNo = apply[j].DocDetailedNo,
                                    StudyNo = apply[j].StudyNo,
                                    ProjectNo = apply[j].ProjectNo,
                                    ProjectName = apply[j].ProjectName,
                                    YJAccessionNo = apply[j].YJAccessionNo,
                                    Combinedstate = "1",
                                    OrderUser = "Distribution",
                                    DocOperCode = apply[j].DocOperCode,
                                    HospitalAreaId = apply[j].HospitalAreaCode,
                                    DepCode = apply[j].ExecutiveDepNo,
                                    if_kf = apply[j].AppendMessage
                                };
                                csScheduleRequestListDto.AlyList.Add(scheduleRequestDto);
                            }
                            else
                            {
                                ScheduleRequestDto scheduleRequestDto = new ScheduleRequestDto
                                {
                                    PatientID = apply[j].PatientID,
                                    DocDetailedNo = apply[j].DocDetailedNo,
                                    StudyNo = apply[j].StudyNo,
                                    ProjectNo = apply[j].ProjectNo,
                                    ProjectName = apply[j].ProjectName,
                                    YJAccessionNo = apply[j].YJAccessionNo,
                                    Combinedstate = "1",
                                    OrderUser = "Distribution",
                                    DocOperCode = apply[j].DocOperCode,
                                    HospitalAreaId = apply[j].HospitalAreaCode,
                                    DepCode = apply[j].ExecutiveDepNo,
                                    if_kf = apply[j].AppendMessage
                                };
                                csScheduleRequestListDto.AlyList.Add(scheduleRequestDto);

                                try
                                {
                                    var jsonBody = JsonConvert.SerializeObject(csScheduleRequestListDto);
                                    LogHelper.WriteInfo(LogNames.CSInfo.ToString(), "自动预约接口入参：" + jsonBody);
                                    string Result = RequestApi.SystrohHttpPostByToken(GetAutoBookedInfo, jsonBody, token);
                                    LogHelper.WriteInfo(LogNames.CSInfo.ToString(), "自动预约接口返回：" + Result);
                                    csScheduleRequestListDto.AlyList.Clear();
                                }
                                catch (Exception e)
                                {
                                    LogHelper.WriteInfo(LogNames.CSInfo.ToString(), "超声预约失败了：" + e.ToString());
                                    LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                                    csScheduleRequestListDto.AlyList.Clear();

                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }

            LogHelper.WriteInfo(LogNames.YJInfo.ToString(), "自动预约任务结束 " + DateTime.Now);

            return Task.CompletedTask;
        }

        public void UpdateDistributionState(string docDetailNo, int state)
        {
            var contextOptionsBuilder = new DbContextOptionsBuilder<YJAppointDbContext>();
            contextOptionsBuilder.UseSqlServer(config["Data:DefaultConnection:ConnectionString"]);
            var db = new YJAppointDbContext(contextOptionsBuilder.Options);

            var entity = db.ApplyInfo.FirstOrDefault(a => a.DocDetailedNo == docDetailNo);
            if (entity != null)
            {
                entity.DistributeStatus = state;
                entity.DistributeTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                db.SaveChanges();
            }
        }
    }
}