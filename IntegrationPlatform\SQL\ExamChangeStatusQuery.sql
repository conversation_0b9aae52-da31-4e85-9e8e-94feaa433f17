-- 申请单状态变更查询SQL
-- 功能：查询门诊患者前一天已预约的申请单，用于状态变更为NW
-- 执行时间：每天早晨5点

DECLARE @yesterdayStart DATETIME = CAST(CAST(DATEADD(DAY, -1, GETDATE()) AS DATE) AS DATETIME)
DECLARE @yesterdayEnd DATETIME = DATEADD(SECOND, -1, DATEADD(DAY, 1, @yesterdayStart))
DECLARE @takeCount INT = 200
DECLARE @executiveDepNos NVARCHAR(500) = '' -- 执行科室配置，例如：'1001,1002,1003'

-- 主单关联查询
-- 条件：1.来源:门诊(O) 2.执行科室配置 3.前一天已预约状态(StudyStatus=0)
SELECT TOP (@takeCount) 
    A.AID, 
    A.DocDetailedNo, 
    A.<PERSON>, 
    A.ExecutiveDepNo, 
    A<PERSON>, 
    <PERSON><PERSON>,
    F.<PERSON>Status, 
    F.ScheduleStartTime, 
    F.App<PERSON>o,
    '主单' AS RecordType
FROM [dbo].[ApplyInfo] A(NOLOCK)
LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
WHERE A.PatientSource = 'O'  -- 门诊患者
  AND (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)  -- 主单条件
  AND F.StudyStatus = 0  -- 已预约状态
  AND F.ScheduleStartTime >= @yesterdayStart 
  AND F.ScheduleStartTime <= @yesterdayEnd  -- 前一天的预约时间
  -- 执行科室过滤条件（如果配置了执行科室）
  AND (@executiveDepNos = '' OR A.ExecutiveDepNo IN (
      SELECT value FROM STRING_SPLIT(@executiveDepNos, ',') WHERE RTRIM(value) <> ''
  ))
ORDER BY A.AID DESC

UNION ALL

-- 非主单非合单关联查询
-- 条件：1.来源:门诊(O) 2.执行科室配置 3.前一天已预约状态(StudyStatus=0)
SELECT TOP (@takeCount) 
    A.AID, 
    A.DocDetailedNo, 
    A.PatientID, 
    A.ExecutiveDepNo, 
    A.ProjectNo, 
    A.PatientSource,
    F.StudyStatus, 
    F.ScheduleStartTime, 
    F.AppFormNo,
    '非主单' AS RecordType
FROM [dbo].[ApplyInfo] A(NOLOCK)
LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
WHERE A.PatientSource = 'O'  -- 门诊患者
  AND ISNULL(A.CombinedCode, '') = ''  -- 非主单非合单条件
  AND F.StudyStatus = 0  -- 已预约状态
  AND F.ScheduleStartTime >= @yesterdayStart 
  AND F.ScheduleStartTime <= @yesterdayEnd  -- 前一天的预约时间
  -- 执行科室过滤条件（如果配置了执行科室）
  AND (@executiveDepNos = '' OR A.ExecutiveDepNo IN (
      SELECT value FROM STRING_SPLIT(@executiveDepNos, ',') WHERE RTRIM(value) <> ''
  ))
ORDER BY A.AID DESC

-- 查询结果说明：
-- 1. 查询门诊患者(PatientSource='O')的申请单
-- 2. 分别处理主单和非主单两种情况
-- 3. 关联处置表查找已预约状态(StudyStatus=0)的数据
-- 4. 筛选前一天的预约时间范围
-- 5. 可根据执行科室配置进行过滤
-- 6. 查询结果将用于状态变更为NW(已预约)

-- 使用示例：
-- 1. 设置执行科室过滤：SET @executiveDepNos = '1001,1002,1003'
-- 2. 设置处理数量：SET @takeCount = 500
-- 3. 执行查询获取需要状态变更的申请单数据
