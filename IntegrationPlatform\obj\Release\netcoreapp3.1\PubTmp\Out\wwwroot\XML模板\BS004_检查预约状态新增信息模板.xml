<PRSC_IN010101UV01 ITSVersion="XML_1.0" xmlns="urn:hl7-org:v3" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:hl7-org:v3 ../multicacheschemas/PRSC_IN010101UV01.xsd">
  <!-- 消息流水号 -->
  <id root="2.16.156.10011.2.5.1.1" extension="BS004_SerialNumber" />
  <!-- 消息创建时间 -->
  <creationTime value="BS004_CreationTime" />
  <!-- 消息的服务标识-->
  <interactionId root="2.16.156.10011.2.5.1.2" extension="PRSC_IN010101UV01" />
  <!--处理代码，标识此消息是否是产品、训练、调试系统的一部分。D：调试；P：产品；T：训练 -->
  <processingCode code="P" />
  <!-- 消息处理模式: A(Archive); I(Initial load); R(Restore from archive); T(Current processing) -->
  <processingModeCode />
  <!-- 消息应答: AL(Always); ER(Error/reject only); NE(Never) -->
  <acceptAckCode code="AL" />
  <!-- 接受者 -->
  <receiver typeCode="RCV">
    <device classCode="DEV" determinerCode="INSTANCE">
      <!-- 接受者ID -->
      <id>
        <item root="2.16.156.10011.2.5.1.3" extension="ESB" />
      </id>
    </device>
  </receiver>
  <!-- 发送者 -->
  <sender typeCode="SND">
    <device classCode="DEV" determinerCode="INSTANCE">
      <!-- 发送者ID -->
      <id>
        <item root="2.16.156.10011.2.5.1.3" extension="TZYT" />
      </id>
    </device>
  </sender>
  <!-- 封装的消息内容 -->
  <controlActProcess classCode="CACT" moodCode="EVN">
    <subject typeCode="SUBJ">
      <actAppointment classCode="ACT" moodCode="APT " xsi:nil="false">
        <!--预约号 预约单流水号，全局唯一号-->
        <id extension="BS004_Number" />
        <!--就诊类别:住院/门诊/急诊 -->
        <code code="BS004_PatientSource" codeSystem="2.16.156.10011.2.3.1.271">
          <displayName value="BS004_PatiName" />
        </code>
        <effectiveTime xsi:type="IVL_TS">
          <!-- 预约检查时间 -->
          <any value="BS004_ScheduleTime" />
        </effectiveTime>
        <!--患者信息 -->
        <subject typeCode="SBJ">
          <patient classCode="PAT">
            <!--患者ID -->
            <id>
              <!-- 域ID -->
              <item root="2.16.156.10011.2.5.1.5" extension="BS004_AreaID" />
              <!-- 患者ID -->
              <item root="2.16.156.10011.2.5.1.4" extension="BS004_PatientID" />
              <!--门（急）诊号标识 -->
              <item root="2.16.156.10011.1.10" extension="BS004_PatientFlag" />
              <!--住院号标识-->
              <item root="2.16.156.10011.1.12" extension="BS004_HospFlag" />
              <!-- 就诊次数 -->
              <item root="2.16.156.10011.2.5.1.8" extension="BS004_VisitNo" />
              <!-- 就诊流水号 -->
              <item root="2.16.156.10011.2.5.1.9" extension="BS004_RegisterID" />
              <!-- 预约排序号-->
              <item root="2.16.156.10011.2.5.1.11" extension="BS004_SortID" />
            </id>
            <patientPerson>
              <name xsi:type="LIST_EN">
                <item>
                  <!-- 患者姓名 -->
                  <part value="BS004_PatientName" />
                </item>
              </name>
            </patientPerson>
            <providerOrganization classCode="ORG" determinerCode="INSTANCE">
              <!--病人科室编码-->
              <id>
                <item extension="BS004_ApplyDepNo" root="2.16.156.10011.1.26" />
              </id>
              <!--病人科室名称 -->
              <name xsi:type="BAG_EN">
                <item>
                  <part value="BS004_ApplyDep" />
                </item>
              </name>
              <contactParty classCode="CON" />
            </providerOrganization>
          </patient>
        </subject>
        <!--预约设备 -->
        <reusableDevice typeCode="RDV" xsi:nil="false">
          <manufacturedDevice classCode="MANU ">
            <manufacturedDevice classCode="DEV " determinerCode="INSTANCE ">
              <!-- 预约设备编码 -->
              <id extension="BS004_EquipmentNo" />
              <!-- 预约设备名称 -->
              <name xsi:type="BAG_EN">
                <item>
                  <part value="BS004_EquipmentName" />
                </item>
              </name>
            </manufacturedDevice>
          </manufacturedDevice>
        </reusableDevice>
        <performer typeCode="PRF " xsi:nil="false ">
          <assignedPerson classCode="ASSIGNED">
            <!--预约员编码 -->
            <id>
              <item extension="BS004_ScheduleUserNo" root="2.16.156.10011.1.4" />
            </id>
            <!--预约员姓名-->
            <assignedPerson determinerCode="INSTANCE" classCode="PSN">
              <name xsi:type="BAG_EN">
                <item>
                  <part value="BS004_ScheduleUser" />
                </item>
              </name>
            </assignedPerson>
          </assignedPerson>
        </performer>
        <!--执行科室 -->
        <location typeCode="LOC " xsi:nil="false">
          <serviceDeliveryLocation classCode="SDLOC ">
            <!--执行科室编码 -->
            <id>
              <item extension="BS004_ExecutiveDepNo" root="2.16.156.10011.1.26" />
            </id>
            <!--执行科室名称 -->
            <addr xsi:type="BAG_AD">
              <item>
                <part value="BS004_ExecutiveDepName" />
              </item>
            </addr>
          </serviceDeliveryLocation>
        </location>
        <!--检查申请单编号 -->
        <referencedOrder typeCode="OREF" xsi:nil="false">
          <actOrder classCode="ACT" moodCode="RQO">
            <!--此处指的是需要进行预约的检查的检查申请单号 -->
            <id root="2.16.156.10011.1.24" extension="BS004_DocGroupNo" />
          </actOrder>
        </referencedOrder>
        <!-- 医嘱号 -->
        <referencedOrder typeCode="OREF">
          <actOrder classCode="ACT" moodCode="RQO">
            <!--此处指的是需要进行预约的检查的检查医嘱号 -->
            <id root="2.16.156.10011.1.28" extension="BS004_DocDetailedNo" />
          </actOrder>
        </referencedOrder>
      </actAppointment>
    </subject>
  </controlActProcess>
	<extra>
		<PatientSex displayName="患者性别">BS004_PatientSex</PatientSex>
		<PatientAge displayName="患者年龄">BS004_PatientAge</PatientAge>
		<PatientTel displayName="电话">BS004_PatientTel</PatientTel>
		<Address displayName="住址">BS004_Address</Address>
		<IDCard displayName="身份证号">BS004_IDCard</IDCard>
		<DocMarkName displayName="申请医生">BS004_DocMarkName</DocMarkName>
		<DocSchedeleDate displayName="申请时间">BS004_DocSchedeleDate</DocSchedeleDate>
		<ClinicalDiagnosis displayName="临床诊断">BS004_ClinicalDiagnosis</ClinicalDiagnosis>
		<ProjectNo display="检查项目代码">BS004_ProjectNo</ProjectNo>
		<ProjectName display="检查项目名称">BS004_ProjectName</ProjectName>
		<ProjectPrice display="费用">BS004_ProjectPrice</ProjectPrice>
		<Purpose display="申请病区">BS004_InpatiAreaNo</Purpose>
		<Purpose display="申请病区代码">BS004_InpatiAreaName</Purpose>
		<Purpose display="特殊备注">BS004_OtherBZ</Purpose>
	</extra>
</PRSC_IN010101UV01>