﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using IntegrationPlatform.Common;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Quartz;
using YJAppoint.Models;
using YJAppoint.Tool;
using Microsoft.Extensions.Configuration;
using System.Data.Common;
using System.Data.SqlClient;
using Newtonsoft.Json;
using IntegrationPlatform.Models;
using System.Text;
using IntegrationPlatform.Common.ApiHelper;
using Newtonsoft.Json.Linq;
using IntegrationPlatform.Areas.Models;
using System.Xml;
using RestSharp;
using System.Net;
using System.IO;
using System.Text.RegularExpressions;
using System.Xml.Serialization;
using IntegrationPlatform.Areas.SendOrder.Models;
using System.Threading;
using System.Web;

namespace IntegrationPlatform.Areas.Business
{
    [DisallowConcurrentExecution]
    public class SendOrderJob : IJob
    {
        private string HospitalNo;
        private string DocNo;
        private string result;
        private string MsgType;
        private string resultCode = "-1"; //是否推送申请单信息成功
        private DateTime? updateTime = null;

        public IConfiguration config { get; }
        private readonly IHostingEnvironment _hostingEnvironment;

        public SendOrderJob(IServiceProvider serviceProvider, IHostingEnvironment hostingEnvironment, IConfiguration Configuration)
        {
            _hostingEnvironment = hostingEnvironment;
            config = Configuration;
        }

       

        public Task Execute(IJobExecutionContext context)
        {
            #region 重新声明一下上下文

            var contextOptionsBuilder = new DbContextOptionsBuilder<YJAppointDbContext>();
            contextOptionsBuilder.UseSqlServer(config["Data:DefaultConnection:ConnectionString"]);
            var db = new YJAppointDbContext(contextOptionsBuilder.Options);

            #endregion 重新声明一下上下文

            try
            {
                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "自动分发作业开始执行");
                string WeChatDeptIDs = config["AppSettings:WeChatDeptIDs"] + "";
                string WeChatSource = config["AppSettings:WeChatSource"] + "";
                var IsMessage = config["AppSettings:IsMessage"] == null ? "" : config["AppSettings:IsMessage"].ToString();
                string MsgUrl = config["AppSettings:SendMsg:MsgUrl"] + "";
                string MessageDeptIDs = config["AppSettings:SendMsg:MessageDeptIDs"] + "";
                string telephoneTest = config["AppSettings:SendMsg:TelephoneTest"];
                string MessageSource = config["AppSettings:SendMsg:MessageSource"] + "";
                string AccountId = config["AppSettings:SendMsg:AccountId"] + "";
                string Password = config["AppSettings:SendMsg:Password"] + "";
                string InterfaceType = config["AppSettings:SendMsg:Type"] + "";
                string WeChatUrl = config["AppSettings:WeChatUrl"] + "";
                string QueueAddLoctionExeNos = config["AppSettings:QueueAddLoctionExeNos"] + "";
                string integrationUrl = config["Url:IntegrationUrl"];
                //要执行的代码逻辑
                //从ApplyInfo表中取出DepNo对应且DistubuteStatus为0、ProjectNo不为空的数据  &&b.ExecutiveDepNo.Equals(ExeDepNo)
                var TempDate = DateTime.Now.AddMinutes(-2);
                List<SendOutMessage> SOMsg = db.SendOutMessage.Where(s => s.ReMegsState == 0).OrderBy(t => t.SOMID).ThenBy(t => t.UpdateTime).ToList();
                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"执行分发服务SendOutMessage Count:{SOMsg.Count()}");
                if (SOMsg != null)
                {
                    string appFormNos = null;
                    for (int j = 0; j < SOMsg.Count(); j++)
                    {
                        //医嘱号
                        DocNo = SOMsg[j].DocDetailedNo;
                        try
                        {
                            string message = "";
                            string notes = string.Empty;
                            string action = string.Empty;
                            string projectName = string.Empty;
                            string projectNo = string.Empty;
                            MsgType = SOMsg[j].SendMegsType;
                            updateTime = SOMsg[j].UpdateTime;
                            int sendMesCount = SOMsg[j].SendMesCount;
                            string MsgStatus = sendMesCount > 0 ? "改约" : "预约";
                            var reMegs = SOMsg[j].ReMegs;
                            //医嘱明细
                            appFormNos = SOMsg[j].DocDetailedNo;

                            List<ApplyInfo> applyInfo = db.ApplyInfo.Include(e => e.ApplyAfterDealInfos).Include(e => e.PatientInfo).Include(e => e.ChargeCompare).Where(b => b.DocDetailedNo.Equals(appFormNos) && !(string.IsNullOrEmpty(b.ProjectNo))).ToList();
                            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"执行分发服务ApplyInfo Count:{applyInfo?.Count()}");
                            if (applyInfo != null)
                            {
                                //短信模版
                                var msgTemplete = db.WebConfig.SingleOrDefault(o => o.WebKey == "MsgTempleteSetting");
                                for (int i = 0; i < applyInfo.Count(); i++)
                                {
                                    if (!string.IsNullOrEmpty(MsgType))
                                    {
                                        //患者信息
                                        PatientInfo patient = applyInfo[i].PatientInfo;
                                        var jqzy = applyInfo[i].ChargeCompare;
                                        notes = jqzy.PreCheckCautions;
                                        if (!string.IsNullOrEmpty(reMegs))
                                        {
                                            #region 预约/改约 推送接口
                                            var bookedReMsg = XmlUtil.XmlDeserialize<BookedInfoReMsg>(reMegs, Encoding.UTF8);
                                            List<string> typeList = new List<string> { "预约" };
                                            if (typeList.Contains(MsgType))
                                            {
                                                action = sendMesCount == 0 ? "CheckAppointStatusInfoAdd" : "CheckAppointStatusInfoUpdate";
                                                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"执行分发服务MsgType:{MsgType},action:{action}");
                                                //判断是否存在合单,合单的话拆分推送
                                                if (!string.IsNullOrWhiteSpace(bookedReMsg.DocDetailedNos))
                                                {
                                                    #region 合单拆分推送消息逻辑
                                                    var docDetailedNoArr = bookedReMsg.DocDetailedNos.Split(',');
                                                    var bookedDetailList = bookedReMsg.BookedDetailDTOList;
                                                    for (int k = 0; k < docDetailedNoArr.Length; k++)
                                                    {
                                                        if (!string.IsNullOrWhiteSpace(docDetailedNoArr[k]))
                                                        {
                                                            //发送短信
                                                            if (docDetailedNoArr[k].ToString() != bookedReMsg.DocDetailedNo)
                                                            {
                                                                //子单注意事项
                                                                var Info = bookedDetailList?.FirstOrDefault(d => d.DocDetailedNo == docDetailedNoArr[k].ToString() && d.DocDetailedNo != bookedReMsg.DocDetailedNo);
                                                                notes = Info?.PreCheckCautions;
                                                                projectName = Info?.ProjectName;
                                                                projectNo = Info?.ProjectNo;
                                                            }
                                                            else
                                                            {
                                                                projectName = applyInfo[i].ProjectName;
                                                                projectNo = applyInfo[i].ProjectNo;
                                                                notes = jqzy.PreCheckCautions;
                                                            }

                                                            #region 推送预约消息
                                                            var apInfo = db.ApplyInfo.Where(d => d.DocDetailedNo == docDetailedNoArr[k].ToString()).FirstOrDefault();
                                                            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"执行分发服务,开始获取推送消息模板内容：合单拆分{docDetailedNoArr[k]}");
                                                            message = GetMessage(docDetailedNoArr[k], bookedReMsg, applyInfo[i], patient, sendMesCount, MsgType, db, projectName, projectNo, apInfo);
                                                            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "集成平台的BS004接收接口请求xml：" + message);
                                                            string res = CallByXML(integrationUrl, action, message);
                                                            resultCode = ResolutionXml(res);
                                                            if (resultCode == "AA")
                                                            {
                                                                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"集成平台的BS004接收返回成功：DetailedNo:{docDetailedNoArr[k]}");
                                                                UpdateStatus(db, "1", docDetailedNoArr[k], MsgType, updateTime);
                                                                SendShortMessage(db, IsMessage, MsgType, MessageDeptIDs, MessageSource, applyInfo[i], patient, bookedReMsg, notes, telephoneTest, MsgUrl, AccountId, Password, projectName, DocNo,
                                                                                                     updateTime, MsgStatus, msgTemplete);
                                                            }
                                                            else
                                                            {
                                                                UpdateStatus(db, "-1", DocNo, MsgType, updateTime);
                                                            }
                                                            #endregion
                                                        }
                                                    }
                                                    #endregion
                                                }
                                                else
                                                {
                                                    #region 非合单推送消息
                                                    LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"执行分发服务,开始获取推送消息模板内容：非合单{DocNo}");
                                                    //获取推送消息模板内容
                                                    message = GetMessage(DocNo, bookedReMsg, applyInfo[i], patient, sendMesCount, MsgType, db, applyInfo[i].ProjectName, applyInfo[i].ProjectNo, applyInfo[i]);
                                                    LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "集成平台的BS004接收接口请求xml：" + message);
                                                    //集成平台的BS004 接收 
                                                    string res = CallByXML(integrationUrl, action, message);
                                                    //LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "集成平台的BS004接收接口返回xml：" + res);
                                                    resultCode = ResolutionXml(res);
                                                    if (resultCode == "AA")
                                                    {
                                                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"集成平台的BS004接收返回成功：DetailedNo:{DocNo}");
                                                        UpdateStatus(db, "1", DocNo, MsgType, updateTime);
                                                        SendShortMessage(db, IsMessage, MsgType, MessageDeptIDs, MessageSource, applyInfo[i], patient, bookedReMsg, notes, telephoneTest, MsgUrl, AccountId, Password, bookedReMsg.ProjectName, DocNo,
                                                                                                     updateTime, MsgStatus, msgTemplete);
                                                    }
                                                    else
                                                    {
                                                        UpdateStatus(db, "-1", DocNo, MsgType, updateTime);
                                                    }
                                                    #endregion
                                                }

                                                #region 仅主单发送短信废弃
                                                //if (resultCode == "AA")
                                                //{

                                                //    #region 短信消息推送  
                                                //    //MessageDeptIDs为空，则走默认所有科室模板一致，否则根据配置的科室走不同的模板
                                                //    if (IsMessage == "true" && MsgType == "预约" && (MessageDeptIDs.Contains("," + applyInfo[i].ExecutiveDepNo + ",") || string.IsNullOrEmpty(MessageDeptIDs)) && MessageSource.Contains("," + applyInfo[i].PatientSource + ","))
                                                //    {
                                                //        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "短信消息推送开始");
                                                //        string errorMsg;
                                                //        var MsgTemplete = db.WebConfig.Where(o => o.WebKey == "MsgTempleteSetting").SingleOrDefault();
                                                //        if (MsgTemplete != null)
                                                //        {
                                                //            string msg = GetMsgTempleteStr(applyInfo[i].ExecutiveDepNo, applyInfo[i].PatientSource, MsgTemplete.WebValue, out errorMsg);
                                                //            string SendMsg = msg.Replace("{患者名称}", patient.PatientName).Replace("{性别}", patient.Sex).Replace("{预约}", MsgStatus)
                                                //                .Replace("{检查项目}", applyInfo[i].ProjectName).Replace("{预约日期}", bookedReMsg.BookedDate)
                                                //                .Replace("{预约时段}", bookedReMsg.BookedTime).Replace("{排序号}", bookedReMsg.ScheduleNumber).Replace("{检查室名称}", bookedReMsg.ExamRoomName).Replace("{检查地点}", bookedReMsg.BookedAddress).Replace("{注意事项}", notes);
                                                //            var requestDto = new BookedInfoDTO
                                                //            {
                                                //                SendType = "ShortMsg",
                                                //                Telephone = string.IsNullOrEmpty(patient.TelephoneNew) ? patient.Telephone : patient.TelephoneNew,
                                                //                Msg = SendMsg
                                                //            };
                                                //            if (!string.IsNullOrEmpty(telephoneTest))
                                                //            {
                                                //                requestDto.Telephone = telephoneTest;
                                                //            }
                                                //            try
                                                //            {
                                                //                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"短信消息推送入参-MsgUrl:{MsgUrl}\r\n AccountId:{AccountId}\r\n msgBody：{SendMsg}");
                                                //                var client = new RestClient(MsgUrl);
                                                //                client.Timeout = -1;
                                                //                var request = new RestRequest(Method.POST);
                                                //                request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
                                                //                request.AddParameter("apName", AccountId);
                                                //                request.AddParameter("apPassword", Password);
                                                //                request.AddParameter("calledNumber", requestDto.Telephone);
                                                //                request.AddParameter("content", requestDto.Msg);
                                                //                request.AddParameter("srcId", "");
                                                //                request.AddParameter("serviceId", "{\"orgMsgId\":\"123456\"}");
                                                //                request.AddParameter("sendTime", "");
                                                //                IRestResponse response = client.Execute(request);
                                                //                string result = response.Content;
                                                //                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "短信消息推送出参：" + result);
                                                //            }
                                                //            catch (Exception exMsg)
                                                //            {
                                                //                UpdateStatus(db, "-6", DocNo, MsgType, updateTime);//-6代表短信发送异常
                                                //                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "预约消息发送成功，短信发送失败：" + exMsg);
                                                //                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), exMsg);
                                                //                continue;
                                                //            }
                                                //            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "短信消息推送结束");
                                                //        }
                                                //        else
                                                //        {

                                                //            LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString() + "预约消息-短信消息模板为空", null);
                                                //        }
                                                //    }
                                                //    #endregion
                                                //}
                                                #endregion
                                            }
                                            else if (MsgType == "取消预约")
                                            {
                                                #region 取消预约推送消息
                                                action = "CheckAppointStatusInfoUpdate";
                                                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"执行分发服务MsgType:{MsgType},action:{action}");
                                                var afterDealInfo = applyInfo[i].ApplyAfterDealInfos.Where(d => d.AppFormNo == DocNo).FirstOrDefault();
                                                message = GetCancelMessage(DocNo, applyInfo[i], afterDealInfo, patient, 1, db);
                                                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "集成平台的BS004接收接口请求xml：" + message);
                                                string res = CallByXML(integrationUrl, action, message);
                                                //LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "集成平台的BS004接收接口返回xml：" + res);
                                                resultCode = ResolutionXml(res);
                                                if (resultCode == "AA")
                                                {
                                                    LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"集成平台的BS004接收返回成功：DetailedNo:{DocNo}");
                                                    UpdateStatus(db, "1", DocNo, MsgType, updateTime);
                                                }
                                                else
                                                {
                                                    UpdateStatus(db, "-1", DocNo, MsgType, updateTime);
                                                }
                                                #endregion
                                            }

                                            #endregion 预约/改约 推送接口

                                        }
                                        else
                                        {
                                            #region 取消预约推送消息
                                            if (!MsgType.Equals("取消预约"))
                                            {
                                                continue;
                                            }
                                            action = "CheckAppointStatusInfoUpdate";
                                            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"执行分发服务MsgType:{MsgType},action:{action}");
                                            var afterDealInfo = applyInfo[i].ApplyAfterDealInfos.Where(d => d.AppFormNo == DocNo).FirstOrDefault();
                                            message = GetCancelMessage(DocNo, applyInfo[i], afterDealInfo, patient, 1, db);
                                            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "集成平台的BS004接收接口请求xml：" + message);
                                            string res = CallByXML(integrationUrl, action, message);
                                            //LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "集成平台的BS004接收接口返回xml：" + res);
                                            resultCode = ResolutionXml(res);
                                            if (resultCode == "AA")
                                            {
                                                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"集成平台的BS004接收返回成功：DetailedNo:{DocNo}");
                                                UpdateStatus(db, "1", DocNo, MsgType, updateTime);
                                            }
                                            else
                                            {
                                                UpdateStatus(db, "-1", DocNo, MsgType, updateTime);
                                            }
                                            #endregion 取消预约

                                        }
                                    }
                                    else
                                    {
                                        #region 消息类型为空处理逻辑
                                        var sql1 = "update SendOutMessage set ReMegsState = @ReMegsState where DocDetailedNo =@DocDetailedNo and (SendMegsType is null or SendMegsType='')";
                                        var args1 = new DbParameter[]
                                         {
                                                 new SqlParameter { ParameterName = "ReMegsState", Value = -1},
                                                 new SqlParameter { ParameterName = "DocDetailedNo", Value = DocNo }
                                         };

                                        i = db.Database.ExecuteSqlCommand(sql1, args1);
                                        db.SaveChanges();
                                        #endregion
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);

                            //更新SendOutMessage表ReMegsState状态
                            UpdateSOMsgReMegsState(-1, DocNo, db);

                            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "" + DocNo + "发送预约到检消息时出现内部逻辑异常，请尝试重新发送");

                            continue;
                        }
                    }
                }
                Thread.Sleep(2000);
                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "自动分发作业结束");
                return Task.CompletedTask;
            }
            catch (Exception e)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "发送预约到检消息时出现内部逻辑异常，请尝试重新发送");

                return Task.CompletedTask;
            }
        }



        public void SendShortMessage(YJAppointDbContext db,
                            string isMessage,
                            string msgType,
                            string messageDeptIDs,
                            string messageSource,
                            ApplyInfo applyInfo,
                            PatientInfo patient,
                            BookedInfoReMsg bookedReMsg,
                            string notes,
                            string telephoneTest,
                            string msgUrl,
                            string accountId,
                            string password,
                            string projectName,
                            string docNo,
                            DateTime? updateTime, string MsgStatus, WebConfig msgTemplete)
        {
            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"isMessage:{isMessage},msgType:{msgType},messageDeptIDs:{messageDeptIDs},ExecutiveDepNo：{applyInfo.ExecutiveDepNo}");
            // 检查是否需要发送短信
            if (isMessage == "true" && msgType == "预约" &&
                (messageDeptIDs.Contains("," + applyInfo.ExecutiveDepNo + ",") || string.IsNullOrEmpty(messageDeptIDs)) &&
                messageSource.Contains("," + applyInfo.PatientSource + ","))
            {
                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "短信消息推送开始");

                string errorMsg;
                if (msgTemplete != null)
                {
                    // 获取短信模板
                    string msg = GetMsgTempleteStr(applyInfo.ExecutiveDepNo, applyInfo.PatientSource, msgTemplete.WebValue, out errorMsg);
                    string sendMsg = msg.Replace("{患者名称}", patient.PatientName)
                                        .Replace("{性别}", patient.Sex)
                                        .Replace("{预约}", MsgStatus)
                                        .Replace("{检查项目}", projectName)
                                        .Replace("{预约日期}", bookedReMsg.BookedDate)
                                        .Replace("{预约时段}", bookedReMsg.BookedTime)
                                        .Replace("{排序号}", bookedReMsg.ScheduleNumber)
                                        .Replace("{检查室名称}", bookedReMsg.ExamRoomName)
                                        .Replace("{检查地点}", bookedReMsg.BookedAddress)
                                        .Replace("{注意事项}", notes);
                    var requestDto = new BookedInfoDTO
                    {
                        SendType = "ShortMsg",
                        Telephone = string.IsNullOrEmpty(patient.TelephoneNew) ? patient.Telephone : patient.TelephoneNew,
                        Msg = sendMsg
                    };

                    if (!string.IsNullOrEmpty(telephoneTest))
                    {
                        requestDto.Telephone = telephoneTest;
                    }

                    try
                    {
                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"短信消息推送入参-MsgUrl:{msgUrl}\r\n AccountId:{accountId}\r\n msgBody：{sendMsg}");

                        // 使用 RestClient 发送短信
                        var client = new RestClient(msgUrl);
                        client.Timeout = -1;
                        var request = new RestRequest(Method.POST);
                        request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
                        request.AddParameter("apName", accountId);
                        request.AddParameter("apPassword", password);
                        request.AddParameter("calledNumber", requestDto.Telephone);
                        request.AddParameter("content", requestDto.Msg);
                        request.AddParameter("srcId", "");
                        request.AddParameter("serviceId", "{\"orgMsgId\":\"123456\"}");
                        request.AddParameter("sendTime", "");

                        IRestResponse response = client.Execute(request);
                        string result = response.Content;

                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "短信消息推送出参：" + result);
                    }
                    catch (Exception exMsg)
                    {
                        UpdateStatus(db, "-6", docNo, msgType, updateTime); // -6代表短信发送异常
                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "预约消息发送成功，短信发送失败：" + exMsg);
                        LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), exMsg);
                    }

                    LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "短信消息推送结束");
                }
                else
                {
                    LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString() + "预约消息-短信消息模板为空", null);
                }
            }
        }

        private void UpdateStatus(YJAppointDbContext db, string state, string docNo, string msgType, DateTime? updateTime)
        {
            string sql1 = string.Empty;
            if (state == "1")
            {
                sql1 = " update SendOutMessage set ReMegsState =@ReMegsState,SendMesCount +=1,GetReMegsTim = '" + DateTime.Now.ToString() + "' where SendMegsType =@SendMegsType and DocDetailedNo =@DocDetailedNo and UpdateTime =@UpdateTime";
            }
            else
            {
                sql1 = " update SendOutMessage set ReMegsState =@ReMegsState,GetReMegsTim = '" + DateTime.Now.ToString() + "' where SendMegsType =@SendMegsType and DocDetailedNo =@DocDetailedNo and UpdateTime =@UpdateTime";
            }
            var args1 = new DbParameter[] { new SqlParameter { ParameterName = "ReMegsState", Value = state },
                                            new SqlParameter { ParameterName = "SendMegsType", Value = msgType },
                                            new SqlParameter { ParameterName = "DocDetailedNo", Value = docNo },
                                            new SqlParameter { ParameterName = "UpdateTime", Value = updateTime } };
            db.Database.ExecuteSqlCommand(sql1, args1);
            db.SaveChanges();
        }

        

        /// <summary>
        /// 获取消息推送结果
        /// </summary>
        /// <param name="Url"></param>
        /// <param name="action"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public string CallByXML(string Url, string action, string message)
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("p1", action);
            parameters.Add("p2", $"<![CDATA[{message}]]>");
            string xml = $@"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:urn=""urn:hl7-org:v3"">
                               <soapenv:Header/>
                               <soapenv:Body>
                                  <urn:HIPMessageServer>
                                     <urn:action>{parameters["p1"]}</urn:action>
                                     <urn:message>{parameters["p2"]}</urn:message>
                                  </urn:HIPMessageServer>
                               </soapenv:Body>
                            </soapenv:Envelope>";

            WebRequest webRequest = WebRequest.Create(Url);
            webRequest.ContentType = $"text/xml; charset=UTF-8";//application/soap+xml
            webRequest.Method = "POST";
            webRequest.Headers.Add("SOAPAction", "urn:HIPMessageServer");
            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "集成平台的BS004【CallByXML】request：" + xml);
            using (Stream requestStream = webRequest.GetRequestStream())
            {
                byte[] paramBytes = Encoding.UTF8.GetBytes(xml);
                requestStream.Write(paramBytes, 0, paramBytes.Length);
            }

            //响应
            WebResponse webResponse = webRequest.GetResponse();
            string resp = JsonConvert.SerializeObject(webResponse);
            //LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "集成平台的BS004【CallByXML】response：" + resp);
            using (StreamReader myStreamReader = new StreamReader(webResponse.GetResponseStream(), Encoding.UTF8))
            {
                return myStreamReader.ReadToEnd();
            }
        }

        /// <summary>
        /// 解析 SOAP 故障消息
        /// </summary>
        /// <param name="faultXml"></param>
        public void ParseSoapFault(string faultXml)
        {
            // 解析 SOAP 故障消息
            try
            {
                using (StringReader reader = new StringReader(faultXml))
                {
                    XmlDocument doc = new XmlDocument();
                    doc.Load(reader);

                    XmlElement root = doc.DocumentElement;
                    XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                    XmlNode xnode = root.FirstChild;
                    nsmgr.AddNamespace("soap", "http://www.w3.org/2003/05/soap-envelope");
                    XmlNode faultCodeNode = doc.SelectSingleNode("//soap:Code", nsmgr);
                    XmlNode faultStringNode = doc.SelectSingleNode("//soap:Reason/soap:Text", nsmgr);
                    var value = xnode.SelectSingleNode("//zys:payload", nsmgr).InnerText.Trim();
                    if (faultCodeNode != null && faultStringNode != null)
                    {
                        string faultCode = faultCodeNode.InnerText;
                        string faultString = faultStringNode.InnerText;

                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"SOAP Fault Code: {faultCode}");
                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"SOAP Fault String: {faultString}");
                    }
                    else
                    {
                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "Failed to parse SOAP Fault.");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"Error parsing SOAP Fault: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析SOAP格式返回的xml
        /// </summary>
        /// <param name="strxml"></param>
        /// <returns></returns>
        public static string ResolutionXml(string strxml)
        {
            #region test
//            strxml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
//<soap:Envelope xmlns:soap=""http://www.w3.org/2003/05/soap-envelope"" xmlns:types=""urn:hl7-org:v3"">
//	<soap:Body>
//		<types:HIPMessageServerResponse>
//			<types:payload>
//				&lt;MCCI_IN000002UV01 xmlns=""urn:hl7-org:v3"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" ITSVersion=""XML_1.0"" xsi:schemaLocation=""urn:hl7-org:v3 ../multicacheschemas/MCCI_IN000002UV01.xsd""&gt;
//				&lt;id extension=""2c321fcf-04d7-4f8e-beca-6a46254ccc77"" root=""2.16.156.10011.*******""/&gt;
//				&lt;creationTime value=""20240809104748""/&gt;
//				&lt;interactionId extension=""MCCI_IN000002UV01"" root=""2.16.156.10011.*******""/&gt;
//				&lt;processingCode code=""P""/&gt;
//				&lt;processingModeCode/&gt;
//				&lt;acceptAckCode code=""AL""/&gt;
//				&lt;receiver typeCode=""RCV""&gt;
//				&lt;device classCode=""DEV"" determinerCode=""INSTANCE""&gt;
//				&lt;id&gt;
//				&lt;item extension=""@222"" root=""2.16.156.10011.*******""/&gt;
//				&lt;/id&gt;
//				&lt;/device&gt;
//				&lt;/receiver&gt;
//				&lt;sender typeCode=""SND""&gt;
//				&lt;device classCode=""DEV"" determinerCode=""INSTANCE""&gt;
//				&lt;id&gt;
//				&lt;item extension=""ESB"" root=""2.16.156.10011.*******""/&gt;
//				&lt;/id&gt;
//				&lt;/device&gt;
//				&lt;/sender&gt;
//				&lt;acknowledgement typeCode=""AA""&gt;
//				&lt;!--请求消息ID--&gt;
//				&lt;targetMessage&gt;
//				&lt;id extension=""@BS004"" root=""2.16.156.10011.*******""/&gt;
//				&lt;/targetMessage&gt;
//				&lt;acknowledgementDetail&gt;
//				&lt;text value=""接收成功""/&gt;
//				&lt;/acknowledgementDetail&gt;
//				&lt;/acknowledgement&gt;
//				&lt;/MCCI_IN000002UV01&gt;
//			</types:payload>
//		</types:HIPMessageServerResponse>
//	</soap:Body>
//</soap:Envelope>";
            #endregion
            string textCode = "";
            string textContes = "";
            try
            {
                // 使用 XmlSerializer 反序列化 XML 文档
                XmlSerializer xmlFormat = new XmlSerializer(typeof(Envelope));
                using (StringReader reader = new StringReader(strxml))
                {
                    Envelope envelope = (Envelope)xmlFormat.Deserialize(reader);
                    string payload = envelope.Body.HIPMessageServerResponse.Payload;
                    LogHelper.WriteInfo(LogNames.SendInfo.ToString(), $"SOAP Payload: {payload}");
                    McciIn000002UV01 payloadModel = ParsePayload(payload);
                    textCode = payloadModel.Acknowledgement.TypeCode.ToString();
                    textContes = payloadModel.Acknowledgement.AcknowledgementDetail.Text.Value;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), "解析SOAP格式返回的xml失败：" + ex.ToString());
            }
            return textCode;
        }

        // 解析 Payload 字符串
        private static McciIn000002UV01 ParsePayload(string payload)
        {
            XmlSerializer serializer = new XmlSerializer(typeof(McciIn000002UV01));
            using (StringReader reader = new StringReader(payload))
            {
                return (McciIn000002UV01)serializer.Deserialize(reader);
            }
        }

        /// <summary>
        /// 获取推送信息内容
        /// </summary>
        /// <param name="docDetailedNoStr"></param>
        /// <param name="bookedReMsg"></param>
        /// <param name="applyInfo"></param>
        /// <param name="patientInfo"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public string GetMessage(string docDetailedNoStr, BookedInfoReMsg bookedReMsg, ApplyInfo applyInfo, PatientInfo patientInfo, int sendMesCount, string msgType, YJAppointDbContext db, string projectName, string projectNo, ApplyInfo applyInfo1)
        {
            var message = "";
            string scheduleTime = string.Empty;
            try
            {
                XmlDocument xd = new XmlDocument();
                string contentRootPath = _hostingEnvironment.ContentRootPath + "\\wwwroot" + "\\" + "XML模板" + "\\" + "BS004_检查预约状态新增信息模板.xml";
                xd.Load(contentRootPath);
                message = xd.InnerXml;//读取xml文件内容

                #region 赋值相关字段
                string msgTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                string patientSource = applyInfo.PatientSource;
                string patiName = GetSourceName(patientSource);
                string patiArea = patientInfo.InpatientArea?.Replace(patientInfo.Department, "");
                string patiAreaNo = patientInfo.InpatientAreaNo?.Replace(patientInfo.DepartmentNo, "");
                if (msgType != "取消预约")
                {
                    scheduleTime = Convert.ToDateTime(bookedReMsg.BookedDate + " " + bookedReMsg.BookedTime.Split('-')[0] + ":00").ToString("yyyyMMddHHmmss");
                }
                var afterDealInfo = applyInfo.ApplyAfterDealInfos.Where(d => d.AppFormNo == docDetailedNoStr).FirstOrDefault();
                //ScheduleNumberSetting queueInfo = db.ScheduleNumberSetting.Where(d => d.ScheduleRoomOrQueue == (afterDealInfo == null ? "" : afterDealInfo.ScheduleRoomOrQueue)).FirstOrDefault();

                //string queueNo
                //string scheduleUserNo;
                //List<OperationLogs> bz = db.OperationLogs.FromSql("select * from OperationLogs where OperObjectId = '" + applyInfo.DocDetailedNo + "' order by  CreatDateTime desc").ToList();
                var UserInfo = db.UserInfos.FromSql("SELECT * FROM UserInfos where RaleName = '" + bookedReMsg.ScheduleUser + "'").FirstOrDefault();

                #endregion

                #region 更新xml内容
                if (sendMesCount > 0)
                {
                    message = message.Replace("PRSC_IN010101UV01", "PRSC_IN010201UV01");
                }

                message = message.Replace("BS004_SerialNumber", Guid.NewGuid().ToString());//消息流水号
                message = message.Replace("BS004_CreationTime", msgTime);
                message = message.Replace("BS004_Number", applyInfo1.AID.ToString()); //预约单流水号edit by lwc 20250401
                message = message.Replace("BS004_PatientSource", patientSource);
                message = message.Replace("BS004_PatiName", patiName);
                message = message.Replace("BS004_ScheduleTime", scheduleTime);
                message = message.Replace("BS004_AreaID", "01");
                message = message.Replace("BS004_PatientID", applyInfo.PatientID);
                message = message.Replace("BS004_PatientFlag", patientInfo.BLH);//门诊号标识/病人卡号
                message = message.Replace("BS004_HospFlag", patientInfo.BLH);//住院号标识
                message = message.Replace("BS004_VisitNo", applyInfo.DoctorTimes);//就诊次数
                message = message.Replace("BS004_RegisterID", applyInfo1.RegisterID);//就诊流水号
                message = message.Replace("BS004_SortID", bookedReMsg.ScheduleNumber);//到检序号
                message = message.Replace("BS004_PatientName", patientInfo.PatientName);
                message = message.Replace("BS004_ApplyDepNo", applyInfo.ApplyDepNo);
                message = message.Replace("BS004_ApplyDep", applyInfo.ApplyDep);
                message = message.Replace("BS004_EquipmentNo", bookedReMsg?.EquipmentNo);
                message = message.Replace("BS004_EquipmentName", bookedReMsg.ExamRoomName); //edit by lwc 20250401
                message = message.Replace("BS004_ScheduleUserNo", UserInfo?.WorkId);//预约员编码
                message = message.Replace("BS004_ScheduleUser", bookedReMsg.ScheduleUser);
                message = message.Replace("BS004_ExecutiveDepNo", bookedReMsg.ExecutiveDepNo);
                message = message.Replace("BS004_ExecutiveDepName", bookedReMsg.ExecutiveDepName);
                //DONE add by lwc 20250417
                if (patientSource.ToUpper() == "I")
                {

                    message = message.Replace("BS004_DocGroupNo", applyInfo1.DocDetailedNo);//申请单编号
                    message = message.Replace("BS004_DocDetailedNo", applyInfo1.DocGroupNo);//医嘱号
                }
                else
                {
                    //addbylwc20250424
                    message = message.Replace("BS004_DocGroupNo", string.IsNullOrEmpty(applyInfo1.DocGroupNo) ? docDetailedNoStr : applyInfo1.DocGroupNo);//申请单编号edit by lwc 20250424
                    message = message.Replace("BS004_DocDetailedNo", applyInfo1.DocDetailedNo);//医嘱号 edit by lwc 20250424
                }
                message = message.Replace("BS004_PatientSex", bookedReMsg?.Sex);
                message = message.Replace("BS004_PatientAge", bookedReMsg?.Age);
                message = message.Replace("BS004_PatientTel", string.IsNullOrEmpty(patientInfo.Telephone) ? patientInfo.TelephoneNew : patientInfo.Telephone);
                message = message.Replace("BS004_Address", patientInfo?.Adress);
                message = message.Replace("BS004_IDCard", bookedReMsg?.SFZH);
                message = message.Replace("BS004_DocMarkName", applyInfo.DocMarkName);
                message = message.Replace("BS004_DocSchedeleDate", applyInfo1.DocSchedeleDate);//开单时间 edit by lwc 20250401
                message = message.Replace("BS004_ClinicalDiagnosis", applyInfo1.AplicationForm);//edit by lwc 20250408
                message = message.Replace("BS004_ProjectNo", projectNo);
                message = message.Replace("BS004_ProjectName", projectName);
                message = message.Replace("BS004_ProjectPrice", applyInfo1.ProjectPrice);//edit by lwc 20250401
                message = message.Replace("BS004_InpatiAreaName", patiArea);//add by lwc 20250407
                message = message.Replace("BS004_InpatiAreaNo", patiAreaNo);//add by lwc 20250407
                message = message.Replace("BS004_OtherBZ", applyInfo1.OtherBZ);//add by lwc 20250408
                #endregion
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), DocNo + "推送信息内容模板获取失败：" + ex.ToString());
            }

            return message;
        }

        /// <summary>
        /// 获取推送信息内容
        /// </summary>
        /// <param name="docDetailedNoStr"></param>
        /// <param name="bookedReMsg"></param>
        /// <param name="applyInfo"></param>
        /// <param name="patientInfo"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public string GetCancelMessage(string docDetailedNoStr, ApplyInfo applyInfo,ApplyAfterDealInfo afterDealInfo, PatientInfo patientInfo, int sendMesCount, YJAppointDbContext db)
        {
            var message = "";
            try
            {
                XmlDocument xd = new XmlDocument();
                string contentRootPath = _hostingEnvironment.ContentRootPath + "\\wwwroot" + "\\" + "XML模板" + "\\" + "BS004_检查预约状态新增信息模板.xml";
                xd.Load(contentRootPath);
                message = xd.InnerXml;//读取xml文件内容

                #region 赋值相关字段
                string msgTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                string patientSource = applyInfo.PatientSource;
                string patiName = GetSourceName(patientSource);
                string patiArea = patientInfo.InpatientArea?.Replace(patientInfo.Department, "");
                string patiAreaNo = patientInfo.InpatientAreaNo?.Replace(patientInfo.DepartmentNo, "");
                //ScheduleNumberSetting queueInfo = db.ScheduleNumberSetting.Where(d => d.ScheduleRoomOrQueue == (afterDealInfo == null ? "" : afterDealInfo.ScheduleRoomOrQueue)).FirstOrDefault();
                var UserInfo = db.UserInfos.FromSql("SELECT * FROM UserInfos where RaleName = '" + afterDealInfo?.ScheduleUser + "'").FirstOrDefault();

                #endregion

                #region 更新xml内容
                if (sendMesCount > 0)
                {
                    message = message.Replace("PRSC_IN010101UV01", "PRSC_IN010201UV01");
                }

                message = message.Replace("BS004_SerialNumber", Guid.NewGuid().ToString());//消息流水号
                message = message.Replace("BS004_CreationTime", msgTime);
                message = message.Replace("BS004_Number", applyInfo.AID.ToString()); //预约单流水号edit by lwc 20250401
                message = message.Replace("BS004_PatientSource", patientSource);
                message = message.Replace("BS004_PatiName", patiName);
                message = message.Replace("BS004_ScheduleTime", "");
                message = message.Replace("BS004_AreaID", "01");
                message = message.Replace("BS004_PatientID", applyInfo.PatientID);
                message = message.Replace("BS004_PatientFlag", patientInfo.BLH);//门诊号标识/病人卡号
                message = message.Replace("BS004_HospFlag", patientInfo.BLH);//住院号标识
                message = message.Replace("BS004_VisitNo", applyInfo.DoctorTimes);//就诊次数
                message = message.Replace("BS004_RegisterID", applyInfo.RegisterID);//就诊流水号
                message = message.Replace("BS004_SortID", afterDealInfo?.ScheduleNumber);//到检序号
                message = message.Replace("BS004_PatientName", patientInfo.PatientName);
                message = message.Replace("BS004_ApplyDepNo", applyInfo.ApplyDepNo);
                message = message.Replace("BS004_ApplyDep", applyInfo.ApplyDep);
                message = message.Replace("BS004_EquipmentNo", "");
                message = message.Replace("BS004_EquipmentName", afterDealInfo?.ScheduleRoomOrQueue);
                message = message.Replace("BS004_ScheduleUserNo", UserInfo?.WorkId);//预约员编码
                message = message.Replace("BS004_ScheduleUser", afterDealInfo?.ScheduleUser);
                message = message.Replace("BS004_ExecutiveDepNo", applyInfo.ExecutiveDepNo);
                message = message.Replace("BS004_ExecutiveDepName", applyInfo.ExecutiveDepName);
                //DONE add by lwc 20250417
                if (patientSource.ToUpper() == "I")
                {
                    message = message.Replace("BS004_DocGroupNo", applyInfo.DocDetailedNo);//申请单编号
                    message = message.Replace("BS004_DocDetailedNo", applyInfo.DocGroupNo);//医嘱号
                }
                else
                {
                    //addbylwc20250424
                    message = message.Replace("BS004_DocGroupNo", string.IsNullOrEmpty(applyInfo.DocGroupNo) ? docDetailedNoStr : applyInfo.DocGroupNo);//申请单编号edit by lwc 20250424
                    message = message.Replace("BS004_DocDetailedNo", applyInfo.DocDetailedNo);//医嘱号 edit by lwc 20250424
                }
                message = message.Replace("BS004_PatientSex", patientInfo.Sex);
                message = message.Replace("BS004_PatientAge", patientInfo.Age);
                message = message.Replace("BS004_PatientTel", string.IsNullOrEmpty(patientInfo.Telephone) ? patientInfo.TelephoneNew : patientInfo.Telephone);
                message = message.Replace("BS004_Address", patientInfo?.Adress);
                message = message.Replace("BS004_IDCard", patientInfo.SFZH);
                message = message.Replace("BS004_DocMarkName", applyInfo.DocMarkName);
                message = message.Replace("BS004_DocSchedeleDate", applyInfo.DocSchedeleDate);
                message = message.Replace("BS004_ClinicalDiagnosis", applyInfo.AplicationForm);
                message = message.Replace("BS004_ProjectNo", applyInfo.ProjectNo);
                message = message.Replace("BS004_ProjectName", applyInfo.ProjectName);
                message = message.Replace("BS004_ProjectPrice", applyInfo.ProjectPrice);
                message = message.Replace("BS004_InpatiAreaName", patiArea);
                message = message.Replace("BS004_InpatiAreaNo", patiAreaNo);
                message = message.Replace("BS004_OtherBZ", applyInfo.OtherBZ);
                #endregion
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), DocNo + "推送信息内容模板获取失败：" + ex.ToString());
            }

            return message;
        }

        /// <summary>
        /// 获取His的授权token
        /// </summary>
        /// <returns></returns>
        public string GetHisToken()
        {
            var token = "";

            try
            {
                //获取token的Url
                var hisAppTokenUrl = config["AppSettings:HisAppTokenUrl"] + "";
                //应用账号(his平台提供)
                var appName = config["AppSettings:appName"] + "";
                //应用账号(his平台提供)
                var appKey = config["AppSettings:appKey"] + "";

                if (!string.IsNullOrEmpty(hisAppTokenUrl) && !string.IsNullOrEmpty(appName) && !string.IsNullOrEmpty(appKey))
                {
                    //请求数据
                    var postData = "appName=" + appName + "&appKey=" + appKey;
                    //请求token接口获取token
                    var tokenInfo = RequestApi.SentHttpPostFromData(hisAppTokenUrl, postData);

                    LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "HIS的请求token接口返回值：" + tokenInfo);

                    var tokenJson = JObject.Parse(tokenInfo);
                    if ((tokenJson.SelectToken("code") + "") == "0")
                    {
                        //获取成功
                        token = tokenJson.SelectToken("accessToken") + "";
                    }
                    else
                    {
                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "授权Token获取失败：" + tokenInfo);
                    }
                }
                else
                {
                    LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "获取Token信息未配置，请检查【HisAppTokenUrl、appName、appKey】这几个字段值是否配置");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), "Token获取异常：" + ex.ToString());
            }

            return token;
        }


        /// <summary>
        /// 更新申请单状态发送表状态
        /// </summary>
        /// <param name="reMegsState"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public void UpdateSOMsgReMegsState(int reMegsState, string docDetailedNo, YJAppointDbContext db)
        {
            try
            {
                //ReMegsState | -1:异常错误 | 1:正常 | -5:推送预约到检状态失败 | -6:短信消息推送失败 | -7:微信消息推送失败
                string sql1 = "";
                var i = 0;
                if (updateTime != null)
                {
                    if (!string.IsNullOrEmpty(MsgType) && !string.IsNullOrEmpty(DocNo))
                    {
                        sql1 = " update SendOutMessage set ReMegsState = @ReMegsState,GetReMegsTim = '" + DateTime.Now.ToString() + "' where SendMegsType =@SendMegsType and DocDetailedNo =@DocDetailedNo and UpdateTime =@UpdateTime";
                        var args1 = new DbParameter[]
                          {
                          new SqlParameter { ParameterName = "ReMegsState", Value = reMegsState },
                          new SqlParameter { ParameterName = "SendMegsType", Value = MsgType },
                          new SqlParameter { ParameterName = "DocDetailedNo", Value = DocNo },
                          new SqlParameter { ParameterName = "UpdateTime", Value = updateTime }
                          };

                        i = db.Database.ExecuteSqlCommand(sql1, args1);
                        db.SaveChanges();
                    }
                }

                if (i > 0)
                {
                    LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "" + updateTime + "—SendOutMessage表申请单" + docDetailedNo + "的ReMegsState状态值" + reMegsState + "更新成功");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), "SendOutMessage表申请单" + DocNo + "的ReMegsState状态更新异常:" + ex.ToString());
            }
        }

        /// <summary>
        /// 存储影像号
        /// </summary>
        public void SaveImageId(string docDetailedNo, HisResponseDto dto, YJAppointDbContext db)
        {
            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), docDetailedNo + "开始更新影像号" + JsonConvert.SerializeObject(dto));

            if (dto != null && dto.Body != null && !string.IsNullOrWhiteSpace(dto.Body.imageid))
            {
                try
                {
                    //更新申请表信息 NewDocDetailedNo暂存最新影像号
                    var sql1 = "update ApplyInfo set StudyNo=@StudyNo,MirrorStatusNote=@MirrorStatusNote where DocDetailedNo=@DocDetailedNo";
                    var args1 = new DbParameter[]
                     {
                         new SqlParameter { ParameterName = "StudyNo", Value =  dto.Body.imageid },
                         new SqlParameter { ParameterName = "MirrorStatusNote", Value =  dto.Body.imageid },
                         new SqlParameter { ParameterName = "DocDetailedNo", Value = docDetailedNo }
                     };

                    var i = db.Database.ExecuteSqlCommand(sql1, args1);
                    db.SaveChanges();

                    if (i > 0)
                    {
                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), docDetailedNo + "申请表影像号更新成功");
                    }
                    else
                    {
                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), docDetailedNo + "申请表影像号更新失败");
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), docDetailedNo + "—申请表更新影像号异常：" + ex.ToString());
                }

                try
                {
                    //更新处置表信息
                    var sql2 = "update ApplyAfterDealInfo set StudyNo=@StudyNo where AppFormNo=@AppFormNo";
                    var args2 = new DbParameter[]
                     {
                         new SqlParameter { ParameterName = "StudyNo", Value =  dto.Body.imageid },
                         new SqlParameter { ParameterName = "AppFormNo", Value = docDetailedNo }
                     };

                    var j = db.Database.ExecuteSqlCommand(sql2, args2);
                    db.SaveChanges();

                    if (j > 0)
                    {
                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), docDetailedNo + "处置表影像号更新成功");
                    }
                    else
                    {
                        LogHelper.WriteInfo(LogNames.SendInfo.ToString(), docDetailedNo + "处置表影像号更新失败");
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), docDetailedNo + "—处置表更新影像号异常：" + ex.ToString());
                }
            }
            else
            {
                LogHelper.WriteInfo(LogNames.ErrorLog.ToString(), docDetailedNo + "—影像号返回为空");
            }
        }

        /// <summary>
        /// C#调取rest接口
        /// </summary>
        /// <param name="url">地址</param>
        /// <param name="content">内容</param>
        /// <returns></returns>
        public string GetToJson(string url, string content)
        {
            try
            {
                var client = new RestClient(url);
                var request = new RestRequest(Method.POST);
                //client.Execute(request);
                request.Timeout = 10000;
                request.AddParameter("application/xml", content, ParameterType.RequestBody);

                IRestResponse response = client.Execute(request);
                return response.Content; //返回的结果
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);

                return "连接服务器出错：\r\n" + ex.Message;
            }
        }

        private string GetSourceName(string source)
        {
            string result = string.Empty;
            switch (source)
            {
                case "I":
                    result = "住院";//住院
                    break;
                case "O":
                    result = "门诊";//门诊
                    break;
                case "E":
                    result = "急诊"; //急诊 现在划到门诊
                    break;
                case "P":
                    result = "体检"; //普通体检
                    break;
                default:
                    result = "";
                    break;
            };
            return result;
        }

        /// <summary>
        /// 获取短信模板字符串
        /// </summary> 
        /// <param name=""></param>
        /// <returns></returns>
        public string GetMsgTempleteStr(string ExecutiveDepNo, string PatientSource, string MsgTemplete, out string Msg)
        {
            Msg = "";
            var templateStr = "";
            try
            {
                var TempList = JsonConvert.DeserializeObject<List<MsgTempleteSettingDTO>>(MsgTemplete);
                var thisTemplate = TempList.Where(o => o.DeptList.Exists(i =>
                                ("," + i.Code.ToUpper() + ",").Contains("," + ExecutiveDepNo.ToUpper() + ","))).ToList();
                if (thisTemplate == null || thisTemplate.Count == 0)
                {
                    thisTemplate = TempList.Where(o => o.DeptList.Exists(i => i.Code.ToUpper() == "DEFAULT")).ToList();
                }
                if (PatientSource.ToUpper() == "I")
                {
                    var thisContentList = thisTemplate.FirstOrDefault().ContentList.Where(o => o.Source.ToUpper() == "I").FirstOrDefault();
                    templateStr = thisContentList.Template;
                }
                else
                {
                    var thisContentList = thisTemplate.FirstOrDefault().ContentList.Where(o => o.Source.ToUpper() == "O").FirstOrDefault();
                    templateStr = thisContentList.Template;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);

                Msg = ex.Message;
                templateStr = "";
            }

            return templateStr;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="Url"></param>
        /// <param name="action"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public string CallMessageByXML(string msgUrl, string accountId, string password, string interfaceType, string msgBody)
        {
            //try
            //{
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("p1", accountId);
            parameters.Add("p2", password);
            parameters.Add("p3", interfaceType);
            parameters.Add("p4", $"<![CDATA[{msgBody}]]>");
            string xml = $@"<soap:Envelope xmlns:soap=""http://www.w3.org/2003/05/soap-envelope"" xmlns:tem=""http://tempuri.org/"">
                            <soap:Header/>
                            <soap:Body>
                                <tem:Web_ChisRequireData>
                                    <!--Optional:-->
                                    <tem:userName>{parameters["p1"]}</tem:userName>
                                    <!--Optional:-->
                                    <tem:passWord>{parameters["p2"]}</tem:passWord>
                                    <!--Optional:-->
                                    <tem:businessType>{parameters["p3"]}</tem:businessType>
                                    <!--Optional:-->
                                    <tem:params3>{parameters["p4"]}</tem:params3>
                                </tem:Web_ChisRequireData>
                            </soap:Body>
                        </soap:Envelope>";

            WebRequest webRequest = WebRequest.Create(msgUrl);
            webRequest.ContentType = $"application/soap+xml; charset=UTF-8";
            webRequest.Method = "POST";
            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "发送短信【CallMessageByXML】request：" + xml);
            using (Stream requestStream = webRequest.GetRequestStream())
            {
                byte[] paramBytes = Encoding.UTF8.GetBytes(xml);
                requestStream.Write(paramBytes, 0, paramBytes.Length);
            }

            //响应
            WebResponse webResponse = webRequest.GetResponse();
            string resp = JsonConvert.SerializeObject(webResponse);
            LogHelper.WriteInfo(LogNames.SendInfo.ToString(), "发送短信【CallMessageByXML】response：" + resp);
            // 响应处理
            using (StreamReader myStreamReader = new StreamReader(webResponse.GetResponseStream(), Encoding.UTF8))
            {
                //Console.WriteLine(myStreamReader.ReadToEnd());
                return myStreamReader.ReadToEnd();
            }
        }
    }
}