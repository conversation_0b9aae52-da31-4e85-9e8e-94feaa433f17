using IntegrationPlatform.Areas.Models;
using IntegrationPlatform.Common;
using IntegrationPlatform.Common.ApiHelper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Quartz;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using YJAppoint.Models;
using YJAppoint.Tool;

namespace IntegrationPlatform.Areas.Business
{
    [DisallowConcurrentExecution]
    public class ExamChangeStatusScheduleJob : IJob
    {
        private readonly YJAppointDbContext db2;
        private readonly IHostingEnvironment _hostingEnvironment;
        public IConfiguration config { get; }

        private static readonly object _locker = new object();

        public ExamChangeStatusScheduleJob(IServiceProvider serviceProvider, IHostingEnvironment hostingEnvironment, IConfiguration Configuration)
        {
            _hostingEnvironment = hostingEnvironment;
            config = Configuration;
        }

        public Task Execute(IJobExecutionContext context)
        {
            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更任务开始执行 " + DateTime.Now);
            try
            {
                #region 重新声明一下上下文

                var contextOptionsBuilder = new DbContextOptionsBuilder<YJAppointDbContext>();
                string Constr = config["Data:DefaultConnection:ConnectionString"];
                contextOptionsBuilder.UseSqlServer(Constr);
                var db = new YJAppointDbContext(contextOptionsBuilder.Options);

                #endregion 重新声明一下上下文

                #region 读取配置文件
                string ExamAddress = config["Url:ExamChangeNotify"];//申请单状态及内容变化接口(是我们自己的)
                string DeptProjectRule = config["AppSettings:ExamChangeStatusSchedule:DeptProjectRule"] + "";
                //string ExecutiveDepNos = config["AppSettings:ExamChangeStatusSchedule:ExecutiveDepNos"] + "";
                //string ProjectNos = config["AppSettings:ExamChangeStatusSchedule:ProjectNos"] + "";
                string PatientSource = config["AppSettings:ExamChangeStatusSchedule:PatientSource"] + "";
                string StartTime = config["AppSettings:ExamChangeStatusSchedule:StartTime"] + "";
                string EndTime = config["AppSettings:ExamChangeStatusSchedule:EndTime"] + "";
                string TakeCount = config["AppSettings:ExamChangeStatusSchedule:TakeCount"] + "";
                int DaysAgo = Convert.ToInt32(config["AppSettings:ExamChangeStatusSchedule:DaysAgo"]);//处理的数据天数间隔
                string clientName = config["AppSettings:ClientName"];
                var clientInfo = db.ClientInfo.FirstOrDefault(a => a.ClientName == clientName);
                var InDate = DateTime.Now.ToString("yyyyMMddHHmmss");
                var myDigest = EncryptionCodingTool.HashSHACode(clientName + InDate + clientInfo.ClientCode);
                var authStr = EncryptionCodingTool.Encrypt3DES(clientInfo.ClientCode + "$" + InDate + "$" + myDigest, clientInfo.PermitKey);
                string token = clientName + "$" + authStr;

                #endregion 读取配置文件

                ScheduleRequestListDto csScheduleRequestListDto = new ScheduleRequestListDto
                {
                    AlyList = new List<ScheduleRequestDto>()
                };

                // 获取处理数量配置
                int takeCount = !string.IsNullOrWhiteSpace(TakeCount) ? int.Parse(TakeCount) : 200;

                // 解析科室项目规则
                var parsedRules = ParseDeptProjectRules(DeptProjectRule);

                if (!parsedRules.Any())
                {
                    LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "未配置科室项目规则，跳过定时任务执行");
                    return Task.CompletedTask;
                }
                var orConditions = BuildSqlConditions1(parsedRules);

                if (!orConditions.Any())
                {
                    LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "未生成有效的SQL条件，跳过定时任务执行");
                    return Task.CompletedTask;
                }

                DateTime startTime;
                DateTime endTime;
                if (!string.IsNullOrEmpty(StartTime) && !string.IsNullOrEmpty(EndTime))
                {
                    startTime = Convert.ToDateTime(StartTime);
                    endTime = Convert.ToDateTime(EndTime);
                }
                else
                {
                    // 获取日期范围
                    DateTime days = DateTime.Now.AddDays(-DaysAgo).Date;
                    startTime = days;
                    endTime = days.AddDays(1).AddSeconds(-1);
                }

                #region 条件过滤
                // 构建完整的共同筛选条件
                string baseCondition = $@"
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime";

                // 添加患者来源过滤条件
                if (!string.IsNullOrWhiteSpace(PatientSource))
                {
                    var patientSources = PatientSource.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).Select(x => $"'{x.Trim()}'");
                    if (patientSources.Any())
                    {
                        baseCondition += $" AND A.PatientSource IN ({string.Join(",", patientSources)})";
                    }
                }
                else
                {
                    // 如果没有配置患者来源，默认为门诊
                    baseCondition += " AND A.PatientSource = 'O'";
                }


                // 添加科室项目条件
                string finalCondition = baseCondition + $" AND ({string.Join(" OR ", orConditions)})";
                #endregion 条件过滤

                string sqlUnion = $@"
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND {finalCondition}
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND {finalCondition}";

                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"共同筛选条件: {finalCondition}");
                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"执行合并SQL查询: {sqlUnion}");
                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"查询时间范围: {startTime:yyyy-MM-dd HH:mm:ss} 到 {endTime:yyyy-MM-dd HH:mm:ss}");

                // 创建申请单数据集合
                var applyDataList = new List<ApplyDataModel>();

                // 执行合并的SQL查询
                using (var command = db.Database.GetDbConnection().CreateCommand())
                {
                    command.CommandText = sqlUnion;
                    command.Parameters.Add(new System.Data.SqlClient.SqlParameter("@startTime", startTime));
                    command.Parameters.Add(new System.Data.SqlClient.SqlParameter("@endTime", endTime));

                    db.Database.OpenConnection();
                    using (var reader = command.ExecuteReader())
                    {
                        var processedDocNos = new HashSet<string>(); // 用于去重

                        while (reader.Read())
                        {
                            string docDetailedNo = reader.GetString("DocDetailedNo");

                            // 避免重复处理相同的申请单号
                            if (!processedDocNos.Contains(docDetailedNo))
                            {
                                applyDataList.Add(new ApplyDataModel
                                {
                                    AID = reader.GetInt32("AID"),
                                    DocDetailedNo = docDetailedNo,
                                    PatientID = reader.GetString("PatientID"),
                                    ExecutiveDepNo = reader.IsDBNull("ExecutiveDepNo") ? "" : reader.GetString("ExecutiveDepNo"),
                                    ProjectNo = reader.IsDBNull("ProjectNo") ? "" : reader.GetString("ProjectNo"),
                                    PatientSource = reader.IsDBNull("PatientSource") ? "" : reader.GetString("PatientSource"),
                                    StudyStatus = reader.IsDBNull("StudyStatus") ? "" : reader.GetString("StudyStatus"),
                                    ScheduleStartTime = reader.IsDBNull("ScheduleStartTime") ? (DateTime?)null : reader.GetDateTime("ScheduleStartTime"),
                                    AppFormNo = reader.IsDBNull("AppFormNo") ? "" : reader.GetString("AppFormNo"),
                                    RecordType = reader.IsDBNull("RecordType") ? "" : reader.GetString("RecordType")
                                });

                                processedDocNos.Add(docDetailedNo);
                            }
                        }
                    }
                    db.Database.CloseConnection();
                }

                // 循环处理申请单状态变更
                if (applyDataList != null && applyDataList.Count > 0)
                {
                    // 按患者ID和执行科室排序
                    var sortedApplyList = applyDataList.OrderBy(a => a.PatientID).ThenBy(a => a.ExecutiveDepNo).ToList();
                    LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"开始循环处理 {sortedApplyList.Count} 条申请单状态变更");

                    int successCount = 0;
                    int failCount = 0;

                    foreach (var applyData in sortedApplyList)
                    {
                        try
                        {
                            #region 更新申请单状态

                            ExamChangeNotify examChange = new ExamChangeNotify();
                            examChange.DocDetailedNo = applyData.DocDetailedNo;
                            examChange.PatientId = applyData.PatientID;
                            examChange.StudyStatus = "2";

                            string applyInfoJson = JsonConvert.SerializeObject(examChange);
                            HttpExamChangeNotify(ExamAddress, applyInfoJson, token);
                            //LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更,调用ExamChangeNotify释放号源接口，入参：" + applyInfoJson);
                            //var TFResult = RequestApi.SystrohHttpPostByToken(ExamAddress, applyInfoJson, token);
                            //LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更,调用ExamChangeNotify释放号源接口，接口返回值：" + TFResult);

                            successCount++;

                            #endregion 更新申请单状态
                        }
                        catch (Exception e)
                        {
                            failCount++;
                            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),
                                $"[{applyData.RecordType}] 更新申请单状态异常，申请单号：{applyData.DocDetailedNo}，异常信息：{e.Message}");
                            LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                        }
                    }

                    LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),
                        $"申请单状态变更处理完成，总处理：{sortedApplyList.Count} 条，成功：{successCount} 条，失败：{failCount} 条");
                }
                else
                {
                    LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "未找到需要处理的申请单数据");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }

            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更任务结束 " + DateTime.Now);

            return Task.CompletedTask;
        }


        /// <summary>
        /// 调用webapi修改申请单状态接口
        /// </summary>
        public string HttpExamChangeNotify(string ExamAddress, string applyInfoJson, string token)
        {
            string TFResult = string.Empty;
            //请求接口代码锁
            lock (_locker)
            {
                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"申请单状态变更,调用ExamChangeNotify释放号源接口，入参：" + applyInfoJson);

                TFResult = RequestApi.SystrohHttpPostByToken(ExamAddress, applyInfoJson, token);

                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"申请单状态变更,调用ExamChangeNotify释放号源接口，接口返回值：" + TFResult);// 执行需要同步的代码
            }
            return TFResult;
        }

        /// <summary>
        /// 解析科室项目规则配置
        /// </summary>
        /// <param name="deptProjectRules">格式：30601,30602,30302$*7041065$*7041066,30303$*7041060$*7041061</param>
        /// <returns></returns>
        private List<DeptProjectRule> ParseDeptProjectRules(string deptProjectRules)
        {
            var rules = new List<DeptProjectRule>();

            if (string.IsNullOrWhiteSpace(deptProjectRules))
            {
                return rules;
            }

            try
            {
                // 按逗号分割各个配置项
                var configItems = deptProjectRules.Split(',').Where(x => !string.IsNullOrWhiteSpace(x));

                foreach (var configItem in configItems)
                {
                    var trimmedItem = configItem.Trim();

                    if (trimmedItem.Contains('$'))
                    {
                        // 包含$符号，表示科室+特定项目的配置
                        var parts = trimmedItem.Split('$');
                        if (parts.Length >= 2)
                        {
                            var deptNo = parts[0].Trim();

                            // 从第二个元素开始都是项目编号
                            var projectNos = parts.Skip(1)
                                .Where(x => !string.IsNullOrWhiteSpace(x))
                                .Select(x => x.Trim())
                                .ToList();

                            if (!string.IsNullOrWhiteSpace(deptNo) && projectNos.Any())
                            {
                                var rule = new DeptProjectRule
                                {
                                    DeptNo = deptNo,
                                    ProjectNos = projectNos,
                                    IsAllProjects = false
                                };

                                rules.Add(rule);

                                //LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),$"解析规则：科室[{deptNo}]的特定项目[{string.Join(",", projectNos)}]");
                            }
                        }
                    }
                    else
                    {
                        // 不包含$符号，表示科室的所有项目
                        var deptNo = trimmedItem;

                        if (!string.IsNullOrWhiteSpace(deptNo))
                        {
                            var rule = new DeptProjectRule
                            {
                                DeptNo = deptNo,
                                IsAllProjects = true
                            };

                            rules.Add(rule);

                            //LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),$"解析规则：科室[{deptNo}]的所有项目");
                        }
                    }
                }
            }
            catch (Exception e)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
            }

            return rules;
        }

        /// <summary>
        /// 根据解析的规则构建SQL条件
        /// </summary>
        /// <param name="rules"></param>
        /// <returns></returns>
        private List<string> BuildSqlConditions(List<DeptProjectRule> rules)
        {
            var orConditions = new List<string>();

            foreach (var rule in rules)
            {
                if (rule.IsAllProjects)
                {
                    // 科室的所有项目
                    orConditions.Add($"A.ExecutiveDepNo = '{rule.DeptNo}'");
                    //LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),$"SQL条件：科室[{rule.DeptNo}]的所有项目");
                }
                else if (rule.ProjectNos.Any())
                {
                    // 科室的特定项目
                    var projectInClause = string.Join(",", rule.ProjectNos.Select(p => $"'{p}'"));
                    orConditions.Add($"(A.ExecutiveDepNo = '{rule.DeptNo}' AND A.ProjectNo IN ({projectInClause}))");
                    //LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),$"SQL条件：科室[{rule.DeptNo}]的项目[{string.Join(",", rule.ProjectNos)}]");
                }
            }

            //LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),$"OR条件：{string.Join(" OR ", orConditions)}");

            return orConditions;
        }


        /// <summary>
        /// 根据解析的规则构建优化的SQL条件
        /// </summary>
        /// <param name="rules"></param>
        /// <returns></returns>
        private List<string> BuildSqlConditions1(List<DeptProjectRule> rules)
        {
            var orConditions = new List<string>();

            // 分组处理：分离全项目科室和特定项目科室
            var allProjectDepts = rules.Where(r => r.IsAllProjects).Select(r => r.DeptNo).ToList();
            var specificProjectRules = rules.Where(r => !r.IsAllProjects).ToList();

            // 1. 处理全项目科室（合并为一个IN条件）
            if (allProjectDepts.Any())
            {
                var deptInClause = string.Join(",", allProjectDepts.Select(d => $"'{d}'"));
                orConditions.Add($"A.ExecutiveDepNo IN ({deptInClause})");

                //LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),$"SQL条件：科室[{string.Join(",", allProjectDepts)}]的所有项目");
            }

            // 2. 处理特定项目科室（每个科室单独一个条件）
            foreach (var rule in specificProjectRules)
            {
                if (rule.ProjectNos.Any())
                {
                    var projectInClause = string.Join(",", rule.ProjectNos.Select(p => $"'{p}'"));
                    orConditions.Add($"(A.ExecutiveDepNo = '{rule.DeptNo}' AND A.ProjectNo IN ({projectInClause}))");
                    //LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),$"SQL条件：科室[{rule.DeptNo}]的项目[{string.Join(",", rule.ProjectNos)}]");
                }
            }

            //LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),$"OR条件：{string.Join(" OR ", orConditions)}");

            return orConditions;
        }

        #region 数据模型
        /// <summary>
        /// 申请单数据模型
        /// </summary>
        public class ApplyDataModel
        {
            public int AID { get; set; }
            public string DocDetailedNo { get; set; }
            public string PatientID { get; set; }
            public string ExecutiveDepNo { get; set; }
            public string ProjectNo { get; set; }
            public string PatientSource { get; set; }
            public string StudyStatus { get; set; }
            public DateTime? ScheduleStartTime { get; set; }
            public string AppFormNo { get; set; }
            public string RecordType { get; set; }
        }

        public class DeptProjectRule
        {
            public string DeptNo { get; set; }
            public List<string> ProjectNos { get; set; }
            public bool IsAllProjects { get; set; }

            public DeptProjectRule()
            {
                ProjectNos = new List<string>();
            }
        }
        #endregion
    }
}