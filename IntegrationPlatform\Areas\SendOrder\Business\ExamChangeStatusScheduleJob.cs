﻿using IntegrationPlatform.Areas.Models;
using IntegrationPlatform.Common;
using IntegrationPlatform.Common.ApiHelper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using YJAppoint.Models;
using YJAppoint.Tool;

namespace IntegrationPlatform.Areas.Business
{
    [DisallowConcurrentExecution]
    public class ExamChangeStatusScheduleJob : IJob
    {
        private readonly YJAppointDbContext db2;
        private readonly IHostingEnvironment _hostingEnvironment;
        public IConfiguration config { get; }

        public ExamChangeStatusScheduleJob(IServiceProvider serviceProvider,  IHostingEnvironment hostingEnvironment, IConfiguration Configuration)
        {
            _hostingEnvironment = hostingEnvironment;
            config = Configuration;
        }

        public Task Execute(IJobExecutionContext context)
        {
            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更任务开始执行 " + DateTime.Now);
            try
            {
                #region 重新声明一下上下文

                var contextOptionsBuilder = new DbContextOptionsBuilder<YJAppointDbContext>();
                string Constr = config["Data:DefaultConnection:ConnectionString"];
                contextOptionsBuilder.UseSqlServer(Constr);
                var db = new YJAppointDbContext(contextOptionsBuilder.Options);

                #endregion 重新声明一下上下文

                #region 读取配置文件
                string ExamAddress = config["Url:ExamChangeNotify"];//申请单状态及内容变化接口(是我们自己的)
                string ExecutiveDepNos = config["AppSettings:ExamChangeStatusSchedule:ExecutiveDepNos"] + "";
                string ProjectNos = config["AppSettings:ExamChangeStatusSchedule:ProjectNos"] + "";
                string PatientSource = config["AppSettings:ExamChangeStatusSchedule:PatientSource"] + "";
                string TakeCount = config["AppSettings:ExamChangeStatusSchedule:TakeCount"] + "";
                string clientName = config["AppSettings:ClientName"];
                var clientInfo = db.ClientInfo.FirstOrDefault(a => a.ClientName == clientName);
                var InDate = DateTime.Now.ToString("yyyyMMddHHmmss");
                var myDigest = EncryptionCodingTool.HashSHACode(clientName + InDate + clientInfo.ClientCode);
                var authStr = EncryptionCodingTool.Encrypt3DES(clientInfo.ClientCode + "$" + InDate + "$" + myDigest, clientInfo.PermitKey);
                string token = clientName + "$" + authStr;

                #endregion 读取配置文件

                ScheduleRequestListDto csScheduleRequestListDto = new ScheduleRequestListDto
                {
                    AlyList = new List<ScheduleRequestDto>()
                };
                //自动预约缓冲等待时间 单位：秒
                var autoScheduleTimeBuffer = !string.IsNullOrWhiteSpace(config["AppSettings:AutoScheduleTimeBuffer"]) ? -Double.Parse(config["AppSettings:AutoScheduleTimeBuffer"]) : -10;
                var TempDate = DateTime.Now.AddSeconds(autoScheduleTimeBuffer);

                // 获取处理数量配置
                int takeCount = !string.IsNullOrWhiteSpace(TakeCount) ? int.Parse(TakeCount) : 200;

                // 构建查询条件
                var query1 = db.ApplyInfo.Where(b => b.DistributeStatus == 0 && b.CreateTime <= TempDate);
                var query2 = db.ApplyInfo.Where(b => b.DistributeStatus == 0 && b.CreateTime > TempDate);

                // 根据配置过滤执行科室
                if (!string.IsNullOrWhiteSpace(ExecutiveDepNos))
                {
                    var depNos = ExecutiveDepNos.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
                    if (depNos.Any())
                    {
                        query1 = query1.Where(b => depNos.Contains(b.ExecutiveDepNo));
                        query2 = query2.Where(b => depNos.Contains(b.ExecutiveDepNo));
                    }
                }

                // 根据配置过滤项目代码
                if (!string.IsNullOrWhiteSpace(ProjectNos))
                {
                    var projectNos = ProjectNos.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
                    if (projectNos.Any())
                    {
                        query1 = query1.Where(b => projectNos.Contains(b.ProjectNo));
                        query2 = query2.Where(b => projectNos.Contains(b.ProjectNo));
                    }
                }

                // 根据配置过滤患者来源
                if (!string.IsNullOrWhiteSpace(PatientSource))
                {
                    var patientSources = PatientSource.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
                    if (patientSources.Any())
                    {
                        query1 = query1.Where(b => patientSources.Contains(b.PatientSource));
                        query2 = query2.Where(b => patientSources.Contains(b.PatientSource));
                    }
                }

                var apply1 = query1.OrderByDescending(o => o.AID).Take(takeCount).ToList();
                var apply2 = query2.OrderByDescending(a => a.AID).Take(takeCount).ToList();
                var apply3 = new List<ApplyInfo>();
                foreach (var item in apply1)
                {
                    foreach (var item2 in apply2)
                    {
                        if (item.PatientID == item2.PatientID && (apply3.Where(a => a.DocDetailedNo == item2.DocDetailedNo).Count() == 0))
                        {
                            apply3.Add(item2);
                        }
                    }
                }
                apply3.AddRange(apply1);

                if (apply3 != null && apply3.ToList().Count > 0)
                {
                    var apply = apply3.OrderBy(a => a.PatientID).OrderBy(a => a.ExecutiveDepNo).ToList();
                    for (int j = 0; j < apply.Count; j++)
                    {

                        try
                        {
                            if (apply[j].DocOperCode == "NW")
                            {
                                #region ExamChangeNotify接口

                                ExamChangeNotify examChange = new ExamChangeNotify();
                                examChange.DocDetailedNo = apply[j].DocDetailedNo;
                                examChange.PatientId = apply[j].PatientID;
                                examChange.StudyStatus = "2";
                                examChange.StudyStartTime = DateTime.Now.ToString();
                                string applyInfoJson = JsonConvert.SerializeObject(examChange);
                                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更,调用ExamChangeNotify释放号源接口，入参：" + applyInfoJson);
                                var TFResult = RequestApi.SystrohHttpPostByToken(ExamAddress, applyInfoJson, token);
                                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更,调用ExamChangeNotify释放号源接口，接口返回值：" + TFResult);

                                #endregion 调用ExamChangeNotify接口
                            }
                        }
                        catch (Exception e)
                        {
                            LogHelper.WriteInfo(LogNames.CancelFeeApp.ToString(), "患者退费,调用ExamChangeNotify释放号源接口【异常】：" + e);
                            LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }

            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更任务结束 " + DateTime.Now);

            return Task.CompletedTask;
        }
    }
}