using IntegrationPlatform.Areas.Models;
using IntegrationPlatform.Common;
using IntegrationPlatform.Common.ApiHelper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using YJAppoint.Models;
using YJAppoint.Tool;

namespace IntegrationPlatform.Areas.Business
{
    /// <summary>
    /// 申请单数据模型
    /// </summary>
    public class ApplyDataModel
    {
        public int AID { get; set; }
        public string DocDetailedNo { get; set; }
        public string PatientID { get; set; }
        public string ExecutiveDepNo { get; set; }
        public string ProjectNo { get; set; }
        public string PatientSource { get; set; }
        public int StudyStatus { get; set; }
        public DateTime? ScheduleStartTime { get; set; }
        public string AppFormNo { get; set; }
        public string RecordType { get; set; }
    }

    [DisallowConcurrentExecution]
    public class ExamChangeStatusScheduleJob : IJob
    {
        private readonly YJAppointDbContext db2;
        private readonly IHostingEnvironment _hostingEnvironment;
        public IConfiguration config { get; }

        public ExamChangeStatusScheduleJob(IServiceProvider serviceProvider,  IHostingEnvironment hostingEnvironment, IConfiguration Configuration)
        {
            _hostingEnvironment = hostingEnvironment;
            config = Configuration;
        }

        public Task Execute(IJobExecutionContext context)
        {
            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更任务开始执行 " + DateTime.Now);
            try
            {
                #region 重新声明一下上下文

                var contextOptionsBuilder = new DbContextOptionsBuilder<YJAppointDbContext>();
                string Constr = config["Data:DefaultConnection:ConnectionString"];
                contextOptionsBuilder.UseSqlServer(Constr);
                var db = new YJAppointDbContext(contextOptionsBuilder.Options);

                #endregion 重新声明一下上下文

                #region 读取配置文件
                string ExamAddress = config["Url:ExamChangeNotify"];//申请单状态及内容变化接口(是我们自己的)
                string ExecutiveDepNos = config["AppSettings:ExamChangeStatus:ExecutiveDepNos"] + "";
                string ProjectNos = config["AppSettings:ExamChangeStatus:ProjectNos"] + "";
                string PatientSource = config["AppSettings:ExamChangeStatus:PatientSource"] + "";
                string TakeCount = config["AppSettings:ExamChangeStatus:TakeCount"] + "";
                string clientName = config["AppSettings:ClientName"];
                var clientInfo = db.ClientInfo.FirstOrDefault(a => a.ClientName == clientName);
                var InDate = DateTime.Now.ToString("yyyyMMddHHmmss");
                var myDigest = EncryptionCodingTool.HashSHACode(clientName + InDate + clientInfo.ClientCode);
                var authStr = EncryptionCodingTool.Encrypt3DES(clientInfo.ClientCode + "$" + InDate + "$" + myDigest, clientInfo.PermitKey);
                string token = clientName + "$" + authStr;

                #endregion 读取配置文件

                ScheduleRequestListDto csScheduleRequestListDto = new ScheduleRequestListDto
                {
                    AlyList = new List<ScheduleRequestDto>()
                };
                //自动预约缓冲等待时间 单位：秒
                var autoScheduleTimeBuffer = !string.IsNullOrWhiteSpace(config["AppSettings:AutoScheduleTimeBuffer"]) ? -Double.Parse(config["AppSettings:AutoScheduleTimeBuffer"]) : -10;
                var TempDate = DateTime.Now.AddSeconds(autoScheduleTimeBuffer);

                // 获取处理数量配置
                int takeCount = !string.IsNullOrWhiteSpace(TakeCount) ? int.Parse(TakeCount) : 200;

                // 构建SQL查询条件
                string executiveDepCondition = "";
                if (!string.IsNullOrWhiteSpace(ExecutiveDepNos))
                {
                    var depNos = ExecutiveDepNos.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).Select(x => $"'{x.Trim()}'");
                    if (depNos.Any())
                    {
                        executiveDepCondition = $" AND A.ExecutiveDepNo IN ({string.Join(",", depNos)}) ";
                    }
                }

                // 获取前一天的日期范围
                DateTime yesterday = DateTime.Now.AddDays(-1).Date;
                DateTime yesterdayStart = yesterday;
                DateTime yesterdayEnd = yesterday.AddDays(1).AddSeconds(-1);

                // 构建原生SQL查询 - 主单关联
                string sqlMain = $@"
                    SELECT TOP {takeCount} A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE A.PatientSource = 'O'  -- 门诊患者
                      AND (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)  -- 主单条件
                      AND F.StudyStatus = 0  -- 已预约状态
                      AND F.ScheduleStartTime >= @yesterdayStart
                      AND F.ScheduleStartTime <= @yesterdayEnd  -- 前一天的预约时间
                      {executiveDepCondition}
                    ORDER BY A.AID DESC";

                // 构建原生SQL查询 - 非主单非合单关联
                string sqlNonMain = $@"
                    SELECT TOP {takeCount} A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE A.PatientSource = 'O'  -- 门诊患者
                      AND ISNULL(A.CombinedCode, '') = ''  -- 非主单非合单条件
                      AND F.StudyStatus = 0  -- 已预约状态
                      AND F.ScheduleStartTime >= @yesterdayStart
                      AND F.ScheduleStartTime <= @yesterdayEnd  -- 前一天的预约时间
                      {executiveDepCondition}
                    ORDER BY A.AID DESC";

                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"执行主单查询SQL: {sqlMain}");
                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"执行非主单查询SQL: {sqlNonMain}");
                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"查询时间范围: {yesterdayStart:yyyy-MM-dd HH:mm:ss} 到 {yesterdayEnd:yyyy-MM-dd HH:mm:ss}");

                var apply1 = new List<ApplyInfo>();
                var apply2 = new List<ApplyInfo>();

                // 创建申请单数据模型类
                var applyDataList = new List<ApplyDataModel>();

                // 执行主单查询
                using (var command = db.Database.GetDbConnection().CreateCommand())
                {
                    command.CommandText = sqlMain;
                    command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@yesterdayStart", yesterdayStart));
                    command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@yesterdayEnd", yesterdayEnd));

                    db.Database.OpenConnection();
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            applyDataList.Add(new ApplyDataModel
                            {
                                AID = reader.GetInt32("AID"),
                                DocDetailedNo = reader.GetString("DocDetailedNo"),
                                PatientID = reader.GetString("PatientID"),
                                ExecutiveDepNo = reader.IsDBNull("ExecutiveDepNo") ? "" : reader.GetString("ExecutiveDepNo"),
                                ProjectNo = reader.IsDBNull("ProjectNo") ? "" : reader.GetString("ProjectNo"),
                                PatientSource = reader.IsDBNull("PatientSource") ? "" : reader.GetString("PatientSource"),
                                StudyStatus = reader.IsDBNull("StudyStatus") ? 0 : reader.GetInt32("StudyStatus"),
                                ScheduleStartTime = reader.IsDBNull("ScheduleStartTime") ? (DateTime?)null : reader.GetDateTime("ScheduleStartTime"),
                                AppFormNo = reader.IsDBNull("AppFormNo") ? "" : reader.GetString("AppFormNo"),
                                RecordType = "主单"
                            });
                        }
                    }
                    db.Database.CloseConnection();
                }

                // 执行非主单查询
                using (var command = db.Database.GetDbConnection().CreateCommand())
                {
                    command.CommandText = sqlNonMain;
                    command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@yesterdayStart", yesterdayStart));
                    command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@yesterdayEnd", yesterdayEnd));

                    db.Database.OpenConnection();
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            // 检查是否已存在相同的申请单号，避免重复
                            string docDetailedNo = reader.GetString("DocDetailedNo");
                            if (!applyDataList.Any(x => x.DocDetailedNo == docDetailedNo))
                            {
                                applyDataList.Add(new ApplyDataModel
                                {
                                    AID = reader.GetInt32("AID"),
                                    DocDetailedNo = docDetailedNo,
                                    PatientID = reader.GetString("PatientID"),
                                    ExecutiveDepNo = reader.IsDBNull("ExecutiveDepNo") ? "" : reader.GetString("ExecutiveDepNo"),
                                    ProjectNo = reader.IsDBNull("ProjectNo") ? "" : reader.GetString("ProjectNo"),
                                    PatientSource = reader.IsDBNull("PatientSource") ? "" : reader.GetString("PatientSource"),
                                    StudyStatus = reader.IsDBNull("StudyStatus") ? 0 : reader.GetInt32("StudyStatus"),
                                    ScheduleStartTime = reader.IsDBNull("ScheduleStartTime") ? (DateTime?)null : reader.GetDateTime("ScheduleStartTime"),
                                    AppFormNo = reader.IsDBNull("AppFormNo") ? "" : reader.GetString("AppFormNo"),
                                    RecordType = "非主单"
                                });
                            }
                        }
                    }
                    db.Database.CloseConnection();
                }
                   // var apply 将上述的原生sql 转成list集合进行循环

                    for (int j = 0; j < apply.Count; j++)
                    {
                        try
                        {
                            #region 更新申请单状态为已预约(NW)

                            ExamChangeNotify examChange = new ExamChangeNotify();
                            examChange.DocDetailedNo = apply[j].DocDetailedNo;
                            examChange.PatientId = apply[j].PatientID;
                            examChange.StudyStatus = "NW"; // 已预约状态

                            string applyInfoJson = JsonConvert.SerializeObject(examChange);
                            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),
                                $"更新申请单状态为已预约，申请单号：{apply[j].DocDetailedNo}，患者ID：{apply[j].PatientID}，入参：{applyInfoJson}");

                            var result = RequestApi.SystrohHttpPostByToken(ExamAddress, applyInfoJson, token);
                            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),
                                $"更新申请单状态接口返回值：{result}");

                            #endregion 更新申请单状态为已预约(NW)
                        }
                        catch (Exception e)
                        {
                            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),
                                $"更新申请单状态异常，申请单号：{apply[j].DocDetailedNo}，异常信息：{e.Message}");
                            LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                        }
                    }

                    LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"申请单状态变更处理完成，共处理 {apply.Count} 条数据");
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }

            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更任务结束 " + DateTime.Now);

            return Task.CompletedTask;
        }
    }
}