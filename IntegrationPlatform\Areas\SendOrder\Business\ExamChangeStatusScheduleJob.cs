﻿using IntegrationPlatform.Areas.Models;
using IntegrationPlatform.Common;
using IntegrationPlatform.Common.ApiHelper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using YJAppoint.Models;
using YJAppoint.Tool;

namespace IntegrationPlatform.Areas.Business
{
    /// <summary>
    /// 申请单数据模型
    /// </summary>
    public class ApplyDataModel
    {
        public int AID { get; set; }
        public string DocDetailedNo { get; set; }
        public string PatientID { get; set; }
        public string ExecutiveDepNo { get; set; }
        public string ProjectNo { get; set; }
        public string PatientSource { get; set; }
        public int StudyStatus { get; set; }
        public DateTime? ScheduleStartTime { get; set; }
        public string AppFormNo { get; set; }
        public string RecordType { get; set; }
    }

    [DisallowConcurrentExecution]
    public class ExamChangeStatusScheduleJob : IJob
    {
        private readonly YJAppointDbContext db2;
        private readonly IHostingEnvironment _hostingEnvironment;
        public IConfiguration config { get; }

        public ExamChangeStatusScheduleJob(IServiceProvider serviceProvider,  IHostingEnvironment hostingEnvironment, IConfiguration Configuration)
        {
            _hostingEnvironment = hostingEnvironment;
            config = Configuration;
        }

        public Task Execute(IJobExecutionContext context)
        {
            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更任务开始执行 " + DateTime.Now);
            try
            {
                #region 重新声明一下上下文

                var contextOptionsBuilder = new DbContextOptionsBuilder<YJAppointDbContext>();
                string Constr = config["Data:DefaultConnection:ConnectionString"];
                contextOptionsBuilder.UseSqlServer(Constr);
                var db = new YJAppointDbContext(contextOptionsBuilder.Options);

                #endregion 重新声明一下上下文

                #region 读取配置文件
                string ExamAddress = config["Url:ExamChangeNotify"];//申请单状态及内容变化接口(是我们自己的)
                string ExecutiveDepNos = config["AppSettings:ExamChangeStatus:ExecutiveDepNos"] + "";
                string ProjectNos = config["AppSettings:ExamChangeStatus:ProjectNos"] + "";
                string PatientSource = config["AppSettings:ExamChangeStatus:PatientSource"] + "";
                string TakeCount = config["AppSettings:ExamChangeStatus:TakeCount"] + "";
                string clientName = config["AppSettings:ClientName"];
                var clientInfo = db.ClientInfo.FirstOrDefault(a => a.ClientName == clientName);
                var InDate = DateTime.Now.ToString("yyyyMMddHHmmss");
                var myDigest = EncryptionCodingTool.HashSHACode(clientName + InDate + clientInfo.ClientCode);
                var authStr = EncryptionCodingTool.Encrypt3DES(clientInfo.ClientCode + "$" + InDate + "$" + myDigest, clientInfo.PermitKey);
                string token = clientName + "$" + authStr;

                #endregion 读取配置文件

                ScheduleRequestListDto csScheduleRequestListDto = new ScheduleRequestListDto
                {
                    AlyList = new List<ScheduleRequestDto>()
                };
                //自动预约缓冲等待时间 单位：秒
                var autoScheduleTimeBuffer = !string.IsNullOrWhiteSpace(config["AppSettings:AutoScheduleTimeBuffer"]) ? -Double.Parse(config["AppSettings:AutoScheduleTimeBuffer"]) : -10;
                var TempDate = DateTime.Now.AddSeconds(autoScheduleTimeBuffer);

                // 获取处理数量配置
                int takeCount = !string.IsNullOrWhiteSpace(TakeCount) ? int.Parse(TakeCount) : 200;

                // 获取前一天的日期范围
                DateTime yesterday = DateTime.Now.AddDays(-1).Date;
                DateTime yesterdayStart = yesterday;
                DateTime yesterdayEnd = yesterday.AddDays(1).AddSeconds(-1);

                // 构建完整的共同筛选条件
                string executiveDepCondition = $@"
                    A.PatientSource = 'O'  -- 门诊患者
                    AND F.StudyStatus = 0  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd";  // 前一天的预约时间

                // 添加执行科室过滤条件
                if (!string.IsNullOrWhiteSpace(ExecutiveDepNos))
                {
                    var depNos = ExecutiveDepNos.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).Select(x => $"'{x.Trim()}'");
                    if (depNos.Any())
                    {
                        executiveDepCondition += $" AND A.ExecutiveDepNo IN ({string.Join(",", depNos)})";
                    }
                }

                // 添加项目代码过滤条件
                if (!string.IsNullOrWhiteSpace(ProjectNos))
                {
                    var projectNos = ProjectNos.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).Select(x => $"'{x.Trim()}'");
                    if (projectNos.Any())
                    {
                        executiveDepCondition += $" AND A.ProjectNo IN ({string.Join(",", projectNos)})";
                    }
                }

                // 合并的UNION ALL SQL查询
                string sqlUnion = $@"
                    -- 主单关联查询
                    SELECT TOP {takeCount} A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)  -- 主单条件
                      AND {executiveDepCondition}
                    
                    UNION ALL
                    
                    -- 非主单非合单关联查询
                    SELECT TOP {takeCount} A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''  -- 非主单非合单条件
                      AND {executiveDepCondition}
                    
                    ORDER BY AID DESC";

                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"共同筛选条件: {executiveDepCondition}");
                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"执行合并SQL查询: {sqlUnion}");
                LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"查询时间范围: {yesterdayStart:yyyy-MM-dd HH:mm:ss} 到 {yesterdayEnd:yyyy-MM-dd HH:mm:ss}");

                // 创建申请单数据集合
                var applyDataList = new List<ApplyDataModel>();

                // 执行合并的SQL查询
                using (var command = db.Database.GetDbConnection().CreateCommand())
                {
                    command.CommandText = sqlUnion;
                    command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@yesterdayStart", yesterdayStart));
                    command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@yesterdayEnd", yesterdayEnd));

                    db.Database.OpenConnection();
                    using (var reader = command.ExecuteReader())
                    {
                        var processedDocNos = new HashSet<string>(); // 用于去重

                        while (reader.Read())
                        {
                            string docDetailedNo = reader.GetString("DocDetailedNo");

                            // 避免重复处理相同的申请单号
                            if (!processedDocNos.Contains(docDetailedNo))
                            {
                                applyDataList.Add(new ApplyDataModel
                                {
                                    AID = reader.GetInt32("AID"),
                                    DocDetailedNo = docDetailedNo,
                                    PatientID = reader.GetString("PatientID"),
                                    ExecutiveDepNo = reader.IsDBNull("ExecutiveDepNo") ? "" : reader.GetString("ExecutiveDepNo"),
                                    ProjectNo = reader.IsDBNull("ProjectNo") ? "" : reader.GetString("ProjectNo"),
                                    PatientSource = reader.IsDBNull("PatientSource") ? "" : reader.GetString("PatientSource"),
                                    StudyStatus = reader.IsDBNull("StudyStatus") ? 0 : reader.GetInt32("StudyStatus"),
                                    ScheduleStartTime = reader.IsDBNull("ScheduleStartTime") ? (DateTime?)null : reader.GetDateTime("ScheduleStartTime"),
                                    AppFormNo = reader.IsDBNull("AppFormNo") ? "" : reader.GetString("AppFormNo"),
                                    RecordType = reader.IsDBNull("RecordType") ? "" : reader.GetString("RecordType")
                                });

                                processedDocNos.Add(docDetailedNo);
                            }
                        }
                    }
                    db.Database.CloseConnection();
                }

                // 循环处理申请单状态变更
                if (applyDataList != null && applyDataList.Count > 0)
                {
                    // 按患者ID和执行科室排序
                    var sortedApplyList = applyDataList.OrderBy(a => a.PatientID).ThenBy(a => a.ExecutiveDepNo).ToList();
                    LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), $"开始循环处理 {sortedApplyList.Count} 条申请单状态变更");

                    int successCount = 0;
                    int failCount = 0;

                    foreach (var applyData in sortedApplyList)
                    {
                        try
                        {
                            #region 更新申请单状态为已预约(NW)

                            ExamChangeNotify examChange = new ExamChangeNotify();
                            examChange.DocDetailedNo = applyData.DocDetailedNo;
                            examChange.PatientId = applyData.PatientID;
                            examChange.StudyStatus = "2";

                            string applyInfoJson = JsonConvert.SerializeObject(examChange);
                            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),
                                $"[{applyData.RecordType}] 更新申请单状态为已预约，申请单号：{applyData.DocDetailedNo}，患者ID：{applyData.PatientID}，执行科室：{applyData.ExecutiveDepNo}，预约时间：{applyData.ScheduleStartTime:yyyy-MM-dd HH:mm:ss}，入参：{applyInfoJson}");

                            var result = RequestApi.SystrohHttpPostByToken(ExamAddress, applyInfoJson, token);
                            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),
                                $"[{applyData.RecordType}] 申请单号：{applyData.DocDetailedNo}，接口返回值：{result}");

                            successCount++;

                            #endregion 更新申请单状态为已预约(NW)
                        }
                        catch (Exception e)
                        {
                            failCount++;
                            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),
                                $"[{applyData.RecordType}] 更新申请单状态异常，申请单号：{applyData.DocDetailedNo}，患者ID：{applyData.PatientID}，异常信息：{e.Message}");
                            LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), e);
                        }
                    }

                    LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(),
                        $"申请单状态变更处理完成，总处理：{sortedApplyList.Count} 条，成功：{successCount} 条，失败：{failCount} 条");
                }
                else
                {
                    LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "未找到需要处理的申请单数据");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrorLog(LogNames.ErrorLog.ToString(), ex);
            }

            LogHelper.WriteInfo(LogNames.ExamChangeNotify.ToString(), "申请单状态变更任务结束 " + DateTime.Now);

            return Task.CompletedTask;
        }
    }
}