using System;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Transport字段处理逻辑测试
/// </summary>
public class TransportLogicTest
{
    public static void Main()
    {
        Console.WriteLine("=== Transport字段处理逻辑测试 ===\n");

        // 测试用例1：不同绝对值，选择绝对值最大的
        TestCase1();

        // 测试用例2：相同绝对值，选择负值
        TestCase2();

        // 测试用例3：相同绝对值且都为正值，选择第一个
        TestCase3();

        // 测试用例4：只有一条数据
        TestCase4();

        // 测试用例5：根据您提供的JSON数据
        TestCase5();

        Console.WriteLine("\n=== 测试完成 ===");
        Console.ReadKey();
    }

    static void TestCase1()
    {
        Console.WriteLine("测试用例1：不同绝对值，选择绝对值最大的");
        var testData = new List<dynamic>
        {
            new { Transport = "100", DocOperCode = "NW", DocDetailedNo = "7544774" },
            new { Transport = "200", DocOperCode = "CA", DocDetailedNo = "7544774" },
            new { Transport = "-150", DocOperCode = "NW", DocDetailedNo = "7544774" }
        };

        var result = GetLatestApplyInfoByTransport(testData, "7544774");
        Console.WriteLine($"选择结果：Transport={result.Transport}, DocOperCode={result.DocOperCode}");
        Console.WriteLine("预期：Transport=200 (绝对值最大)\n");
    }

    static void TestCase2()
    {
        Console.WriteLine("测试用例2：相同绝对值，选择负值");
        var testData = new List<dynamic>
        {
            new { Transport = "200", DocOperCode = "NW", DocDetailedNo = "7544774" },
            new { Transport = "-200", DocOperCode = "CA", DocDetailedNo = "7544774" },
            new { Transport = "100", DocOperCode = "NW", DocDetailedNo = "7544774" }
        };

        var result = GetLatestApplyInfoByTransport(testData, "7544774");
        Console.WriteLine($"选择结果：Transport={result.Transport}, DocOperCode={result.DocOperCode}");
        Console.WriteLine("预期：Transport=-200 (相同绝对值选择负值)\n");
    }

    static void TestCase3()
    {
        Console.WriteLine("测试用例3：相同绝对值且都为正值，选择第一个");
        var testData = new List<dynamic>
        {
            new { Transport = "200", DocOperCode = "NW", DocDetailedNo = "7544774" },
            new { Transport = "200", DocOperCode = "CA", DocDetailedNo = "7544774" },
            new { Transport = "100", DocOperCode = "NW", DocDetailedNo = "7544774" }
        };

        var result = GetLatestApplyInfoByTransport(testData, "7544774");
        Console.WriteLine($"选择结果：Transport={result.Transport}, DocOperCode={result.DocOperCode}");
        Console.WriteLine("预期：Transport=200, DocOperCode=NW (相同绝对值都为正值选择第一个)\n");
    }

    static void TestCase4()
    {
        Console.WriteLine("测试用例4：只有一条数据");
        var testData = new List<dynamic>
        {
            new { Transport = "631", DocOperCode = "NW", DocDetailedNo = "7544774" }
        };

        var result = GetLatestApplyInfoByTransport(testData, "7544774");
        Console.WriteLine($"选择结果：Transport={result.Transport}, DocOperCode={result.DocOperCode}");
        Console.WriteLine("预期：Transport=631, DocOperCode=NW (唯一数据)\n");
    }

    static void TestCase5()
    {
        Console.WriteLine("测试用例5：根据您提供的JSON数据");
        var testData = new List<dynamic>
        {
            new { Transport = "631", DocOperCode = "NW", DocDetailedNo = "7544774" },
            new { Transport = "-631", DocOperCode = "CA", DocDetailedNo = "7544774" }
        };

        var result = GetLatestApplyInfoByTransport(testData, "7544774");
        Console.WriteLine($"选择结果：Transport={result.Transport}, DocOperCode={result.DocOperCode}");
        Console.WriteLine("预期：Transport=-631, DocOperCode=CA (相同绝对值选择负值-退费)\n");
    }

    /// <summary>
    /// 模拟Transport字段处理逻辑
    /// </summary>
    static dynamic GetLatestApplyInfoByTransport(List<dynamic> applyInfos, string docDetailedNo)
    {
        try
        {
            // 记录所有Transport值
            var transportValues = applyInfos.Select(x => new
            {
                Transport = x.Transport?.ToString() ?? "0",
                DocOperCode = x.DocOperCode?.ToString() ?? "",
                Data = x
            }).ToList();

            Console.WriteLine($"申请单号[{docDetailedNo}]有{applyInfos.Count}条数据，Transport值：[{string.Join(", ", transportValues.Select(t => $"{t.DocOperCode}:{t.Transport}"))}]");

            // 解析Transport为数值并计算绝对值
            var parsedData = transportValues.Select(x => new
            {
                OriginalTransport = x.Transport,
                TransportValue = int.TryParse(x.Transport, out int val) ? val : 0,
                AbsTransportValue = Math.Abs(int.TryParse(x.Transport, out int absVal) ? absVal : 0),
                x.DocOperCode,
                x.Data
            }).ToList();

            // 找到绝对值最大的Transport
            var maxAbsValue = parsedData.Max(x => x.AbsTransportValue);
            var candidatesWithMaxAbs = parsedData.Where(x => x.AbsTransportValue == maxAbsValue).ToList();

            dynamic selectedApplyInfo = null;

            if (candidatesWithMaxAbs.Count == 1)
            {
                // 只有一个最大绝对值，直接选择
                selectedApplyInfo = candidatesWithMaxAbs.First().Data;
                Console.WriteLine($"选择唯一最大绝对值Transport:[{candidatesWithMaxAbs.First().OriginalTransport}]，DocOperCode:[{candidatesWithMaxAbs.First().DocOperCode}]");
            }
            else
            {
                // 多个相同的最大绝对值，优先选择负值（退费）
                var negativeCandidate = candidatesWithMaxAbs.FirstOrDefault(x => x.TransportValue < 0);
                if (negativeCandidate != null)
                {
                    selectedApplyInfo = negativeCandidate.Data;
                    Console.WriteLine($"存在相同绝对值，选择负值Transport:[{negativeCandidate.OriginalTransport}]，DocOperCode:[{negativeCandidate.DocOperCode}]");
                }
                else
                {
                    // 都是正值，选择第一个
                    selectedApplyInfo = candidatesWithMaxAbs.First().Data;
                    Console.WriteLine($"存在相同绝对值且都为正值，选择第一个Transport:[{candidatesWithMaxAbs.First().OriginalTransport}]，DocOperCode:[{candidatesWithMaxAbs.First().DocOperCode}]");
                }
            }

            return selectedApplyInfo;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理Transport字段异常：{ex.Message}，返回第一条数据");
            return applyInfos.FirstOrDefault();
        }
    }
}
