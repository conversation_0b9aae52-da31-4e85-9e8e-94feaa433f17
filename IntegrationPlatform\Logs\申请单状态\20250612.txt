
 
记录时间：2025-06-12 10:08:22,089 
线程ID:[18] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/12 10:08:22

 
记录时间：2025-06-12 10:08:22,570 
线程ID:[18] ExamChangeNotify 
 -未配置科室项目规则，跳过定时任务执行

 
记录时间：2025-06-12 10:10:01,732 
线程ID:[11] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/12 10:10:01

 
记录时间：2025-06-12 10:10:31,650 
线程ID:[11] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo IN ('30601','30602') OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')))

 
记录时间：2025-06-12 10:10:31,651 
线程ID:[11] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo, A.DistributeStatus,
                           A.ProjectNo, A.PatientSource, F.StudyStatus, F.ScheduleStartTime, F.AppFormNo,
                           CASE
                               WHEN A.CombinedCode IS NOT NULL AND A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo THEN '主单'
                               ELSE '非主单'
                           END AS RecordType
                    FROM [dbo].[ApplyInfo] A WITH(NOLOCK)
                    INNER JOIN [dbo].[ApplyAfterDealInfo] F WITH(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (
                           (A.CombinedCode IS NOT NULL AND A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)  -- 主单条件
                           OR
                           (A.CombinedCode IS NULL OR A.CombinedCode = '')  -- 非主单非合单条件
                          )
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo IN ('30601','30602') OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')))

 
记录时间：2025-06-12 10:31:25,253 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/12 10:31:25

 
记录时间：2025-06-12 10:31:25,762 
线程ID:[28] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo IN ('30601','30602') OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')))

 
记录时间：2025-06-12 10:31:25,762 
线程ID:[28] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo, A.DistributeStatus,
                           A.ProjectNo, A.PatientSource, F.StudyStatus, F.ScheduleStartTime, F.AppFormNo,
                           CASE
                               WHEN A.CombinedCode IS NOT NULL AND A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo THEN '主单'
                               ELSE '非主单'
                           END AS RecordType
                    FROM [dbo].[ApplyInfo] A WITH(NOLOCK)
                    INNER JOIN [dbo].[ApplyAfterDealInfo] F WITH(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (
                           (A.CombinedCode IS NOT NULL AND A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)  -- 主单条件
                           OR
                           (A.CombinedCode IS NULL OR A.CombinedCode = '')  -- 非主单非合单条件
                          )
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O') AND (A.ExecutiveDepNo IN ('30601','30602') OR (A.ExecutiveDepNo = '30302' AND A.ProjectNo IN ('*7041065','*7041099')))
