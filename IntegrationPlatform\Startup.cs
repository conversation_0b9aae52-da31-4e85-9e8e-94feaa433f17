﻿using IntegrationPlatform.Areas.Business;
using IntegrationPlatform.Business;
using IntegrationPlatform.Common;
using IntegrationPlatform.QuartzJob;
using log4net.Config;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.HttpsPolicy;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Quartz;
using Quartz.Impl;
using Quartz.Spi;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using YJAppoint.Models;
using YJAppoint.Tool;

namespace IntegrationPlatform
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
            XmlConfigurator.Configure(LogHelper.Repository, new FileInfo("log4net.config"));
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddDbContext<YJAppointDbContext>
             (options =>
              options.UseSqlServer(
              Configuration["Data:DefaultConnection:ConnectionString"]));

            services.AddTransient<UserBusiness>();
            services.AddTransient<WebConfigBusiness>();
            services.AddTransient<SendOrderJob>();
            services.AddTransient<YJ_AutoScheduleJob>();
            services.AddTransient<ExamChangeStatusScheduleJob>();
            //services.AddScoped<CS_AutoScheduleJob>();

            services.AddSingleton<ISchedulerFactory, StdSchedulerFactory>();
            services.AddSingleton<IJobFactory, JobFactory>();

            services.AddOptions();
            services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromHours(10);
                options.Cookie.HttpOnly = true;
            });

            services.AddMvc().SetCompatibilityVersion(CompatibilityVersion.Version_2_1);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostingEnvironment env, Microsoft.Extensions.Hosting.IApplicationLifetime lifetime, IServiceProvider container
            //, SendOrderQuartzBusiness sendOrderQuartzBusiness ,YJ_AutoScheduleQuartzBusiness yjAutoScheduleQuartzBusiness
            , ISchedulerFactory schedulerFactory, IJobFactory jobFactory, IConfiguration conf
            , WebConfigBusiness webConfigBusiness, YJAppointDbContext db
            )
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");
                //app.UseHsts();
            }

            app.UseRouting();
            app.UseSession();//添加会话配置
            app.UseStaticFiles();
            app.UseCookiePolicy();


            //判断是否需要直接开启消息分发服务
            //sendOrderQuartzBusiness.IsDistribution();
            var sendorderBusiness = new SendOrderQuartzBusiness(schedulerFactory, jobFactory, conf);
            sendorderBusiness.IsDistribution();
            //判断是否直接开启自动预约服务
            //yjAutoScheduleQuartzBusiness.IsDistribution();
            var yjAutoScheduleQuartzBusiness =
              new YJ_AutoScheduleQuartzBusiness(schedulerFactory, jobFactory, conf, db, webConfigBusiness);
            yjAutoScheduleQuartzBusiness.IsDistribution();
            ///超声自动预约服务
            //cs_AutoScheduleQuartzBusiness.IsDistribution();
            //判断是否需要直接开启申请单状态变更调度服务
            var examChangeStatusQuartzBusiness =
              new ExamChangeStatusQuartzBusiness(schedulerFactory, jobFactory, conf, db, webConfigBusiness);
            examChangeStatusQuartzBusiness.IsDistribution();

            //app.UseMvc(routes =>
            //{
            //    routes.MapRoute(
            //        name: "default",
            //        template: "{controller=User}/{action=login}/{id?}");
            //    routes.MapRoute("areaRoute", "{area:exists}/{controller}/{action=Index}/{id?}");
            //});
            app.UseEndpoints(routes =>
            {
                routes.MapControllerRoute(name: "default", pattern: "{controller=User}/{action=login}/{id?}");
                routes.MapControllerRoute("areaRoute", "{area:exists}/{controller}/{action=Index}/{id?}");
            });
        }
    }
}