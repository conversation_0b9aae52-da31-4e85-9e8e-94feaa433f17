
 
记录时间：2025-06-09 17:52:24,928 
线程ID:[4] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/9 17:52:24

 
记录时间：2025-06-09 18:07:46,065 
线程ID:[5] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/9 18:07:46

 
记录时间：2025-06-09 18:07:54,722 
线程ID:[5] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:08:00,902 
线程ID:[5] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>,<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>,
                           <PERSON><PERSON>, F.Schedule<PERSON>tTime, F.<PERSON>orm<PERSON>, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:08:59,628 
线程ID:[5] ExamChangeNotify 
 -查询时间范围: 2022-06-01 00:00:00 到 2022-07-01 00:00:00

 
记录时间：2025-06-09 18:10:38,415 
线程ID:[5] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/9 18:10:38

 
记录时间：2025-06-09 18:10:41,887 
线程ID:[5] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:10:42,471 
线程ID:[5] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:10:49,261 
线程ID:[5] ExamChangeNotify 
 -查询时间范围: 2022-06-01 00:00:00 到 2022-07-01 00:00:00

 
记录时间：2025-06-09 18:13:41,969 
线程ID:[14] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/9 18:13:41

 
记录时间：2025-06-09 18:13:45,006 
线程ID:[14] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:13:47,221 
线程ID:[14] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:13:48,101 
线程ID:[14] ExamChangeNotify 
 -查询时间范围: 2022-06-01 00:00:00 到 2022-07-01 00:00:00

 
记录时间：2025-06-09 18:20:53,464 
线程ID:[24] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/9 18:20:53

 
记录时间：2025-06-09 18:20:57,715 
线程ID:[24] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:20:57,715 
线程ID:[24] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:20:57,716 
线程ID:[24] ExamChangeNotify 
 -查询时间范围: 2022-06-01 00:00:00 到 2022-06-01 23:59:59

 
记录时间：2025-06-09 18:21:10,961 
线程ID:[24] ExamChangeNotify 
 -开始循环处理 178 条申请单状态变更

 
记录时间：2025-06-09 18:29:33,033 
线程ID:[28] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/9 18:29:33

 
记录时间：2025-06-09 18:29:38,718 
线程ID:[28] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:29:39,192 
线程ID:[28] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @yesterdayStart 
                    AND F.ScheduleStartTime <= @yesterdayEnd AND A.PatientSource = 'O'

 
记录时间：2025-06-09 18:29:47,103 
线程ID:[28] ExamChangeNotify 
 -查询时间范围: 2022-06-01 00:00:00 到 2022-06-01 23:59:59

 
记录时间：2025-06-09 19:00:00,068 
线程ID:[29] ExamChangeNotify 
 -申请单状态变更任务开始执行 2025/6/9 19:00:00

 
记录时间：2025-06-09 19:07:20,746 
线程ID:[29] ExamChangeNotify 
 -共同筛选条件: 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O')

 
记录时间：2025-06-09 19:07:20,746 
线程ID:[29] ExamChangeNotify 
 -执行合并SQL查询: 
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE (A.CombinedCode <> '' AND A.CombinedCode = A.DocDetailedNo)
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O')
                    
                    UNION ALL
                    
                    SELECT A.AID, A.DocDetailedNo, A.PatientID, A.ExecutiveDepNo,A.DistributeStatus, A.ProjectNo, A.PatientSource,
                           F.StudyStatus, F.ScheduleStartTime, F.AppFormNo, '非主单' AS RecordType
                    FROM [dbo].[ApplyInfo] A(NOLOCK)
                    LEFT JOIN [dbo].[ApplyAfterDealInfo] F(NOLOCK) ON A.DocDetailedNo = F.AppFormNo
                    WHERE ISNULL(A.CombinedCode, '') = ''
                      AND 
                    A.DistributeStatus = 1  -- 已分发
                    AND F.StudyStatus = '0'  -- 已预约状态
                    AND F.ScheduleStartTime >= @startTime 
                    AND F.ScheduleStartTime <= @endTime AND A.PatientSource IN ('O')

 
记录时间：2025-06-09 19:07:20,747 
线程ID:[29] ExamChangeNotify 
 -查询时间范围: 2022-06-01 00:00:00 到 2022-06-01 23:59:59

 
记录时间：2025-06-09 19:07:20,826 
线程ID:[29] ExamChangeNotify 
 -开始循环处理 178 条申请单状态变更

 
记录时间：2025-06-09 19:07:20,861 
线程ID:[29] ExamChangeNotify 
 -申请单状态变更,调用ExamChangeNotify释放号源接口，入参：{"PatientId":"20012166","DocDetailedNo":"417206516","AccessionNo":null,"StudyNo":null,"StudyStatus":"2","CallTime":null,"StudyStartTime":null,"StudyEndTime":null,"StudyUserId":null,"StudyUser":null,"StudyAssistantId":null,"StudyAssistant":null,"StudyModality":null,"JCBW":null,"ReportTime":null,"ReportUser":null,"ReportUserId":null,"AuditTime":null,"AuditUser":null,"AuditUserId":null,"FinalAuditTime":null,"FinalAuditUser":null,"FinalAuditUserId":null,"ReportPrintTime":null,"ReportPrintUser":null,"FilmPrintTime":null,"FilmPrintUser":null,"ReportConclusion":null,"ReportDiscription":null,"ReportRemark":null,"IsNormal":null,"IsNormalDetail":null,"DistributedTime":null,"DistributedStatus":null,"canCelMes":null}

 
记录时间：2025-06-09 19:07:25,758 
线程ID:[29] ExamChangeNotify 
 -[主单] 更新申请单状态异常，申请单号：417206516，异常信息：由于目标计算机积极拒绝，无法连接。 由于目标计算机积极拒绝，无法连接。

 
记录时间：2025-06-09 19:07:25,868 
线程ID:[29] ExamChangeNotify 
 -申请单状态变更,调用ExamChangeNotify释放号源接口，入参：{"PatientId":"20016772","DocDetailedNo":"417172451","AccessionNo":null,"StudyNo":null,"StudyStatus":"2","CallTime":null,"StudyStartTime":null,"StudyEndTime":null,"StudyUserId":null,"StudyUser":null,"StudyAssistantId":null,"StudyAssistant":null,"StudyModality":null,"JCBW":null,"ReportTime":null,"ReportUser":null,"ReportUserId":null,"AuditTime":null,"AuditUser":null,"AuditUserId":null,"FinalAuditTime":null,"FinalAuditUser":null,"FinalAuditUserId":null,"ReportPrintTime":null,"ReportPrintUser":null,"FilmPrintTime":null,"FilmPrintUser":null,"ReportConclusion":null,"ReportDiscription":null,"ReportRemark":null,"IsNormal":null,"IsNormalDetail":null,"DistributedTime":null,"DistributedStatus":null,"canCelMes":null}

 
记录时间：2025-06-09 19:07:30,454 
线程ID:[29] ExamChangeNotify 
 -[主单] 更新申请单状态异常，申请单号：417172451，异常信息：由于目标计算机积极拒绝，无法连接。 由于目标计算机积极拒绝，无法连接。

 
记录时间：2025-06-09 19:07:30,457 
线程ID:[29] ExamChangeNotify 
 -申请单状态变更,调用ExamChangeNotify释放号源接口，入参：{"PatientId":"20019935","DocDetailedNo":"417206922","AccessionNo":null,"StudyNo":null,"StudyStatus":"2","CallTime":null,"StudyStartTime":null,"StudyEndTime":null,"StudyUserId":null,"StudyUser":null,"StudyAssistantId":null,"StudyAssistant":null,"StudyModality":null,"JCBW":null,"ReportTime":null,"ReportUser":null,"ReportUserId":null,"AuditTime":null,"AuditUser":null,"AuditUserId":null,"FinalAuditTime":null,"FinalAuditUser":null,"FinalAuditUserId":null,"ReportPrintTime":null,"ReportPrintUser":null,"FilmPrintTime":null,"FilmPrintUser":null,"ReportConclusion":null,"ReportDiscription":null,"ReportRemark":null,"IsNormal":null,"IsNormalDetail":null,"DistributedTime":null,"DistributedStatus":null,"canCelMes":null}

 
记录时间：2025-06-09 19:07:35,088 
线程ID:[29] ExamChangeNotify 
 -[主单] 更新申请单状态异常，申请单号：417206922，异常信息：由于目标计算机积极拒绝，无法连接。 由于目标计算机积极拒绝，无法连接。

 
记录时间：2025-06-09 19:07:35,089 
线程ID:[29] ExamChangeNotify 
 -申请单状态变更,调用ExamChangeNotify释放号源接口，入参：{"PatientId":"20020569","DocDetailedNo":"417207316","AccessionNo":null,"StudyNo":null,"StudyStatus":"2","CallTime":null,"StudyStartTime":null,"StudyEndTime":null,"StudyUserId":null,"StudyUser":null,"StudyAssistantId":null,"StudyAssistant":null,"StudyModality":null,"JCBW":null,"ReportTime":null,"ReportUser":null,"ReportUserId":null,"AuditTime":null,"AuditUser":null,"AuditUserId":null,"FinalAuditTime":null,"FinalAuditUser":null,"FinalAuditUserId":null,"ReportPrintTime":null,"ReportPrintUser":null,"FilmPrintTime":null,"FilmPrintUser":null,"ReportConclusion":null,"ReportDiscription":null,"ReportRemark":null,"IsNormal":null,"IsNormalDetail":null,"DistributedTime":null,"DistributedStatus":null,"canCelMes":null}

 
记录时间：2025-06-09 19:07:39,720 
线程ID:[29] ExamChangeNotify 
 -[主单] 更新申请单状态异常，申请单号：417207316，异常信息：由于目标计算机积极拒绝，无法连接。 由于目标计算机积极拒绝，无法连接。

 
记录时间：2025-06-09 19:07:39,722 
线程ID:[29] ExamChangeNotify 
 -申请单状态变更,调用ExamChangeNotify释放号源接口，入参：{"PatientId":"20021523","DocDetailedNo":"417236052","AccessionNo":null,"StudyNo":null,"StudyStatus":"2","CallTime":null,"StudyStartTime":null,"StudyEndTime":null,"StudyUserId":null,"StudyUser":null,"StudyAssistantId":null,"StudyAssistant":null,"StudyModality":null,"JCBW":null,"ReportTime":null,"ReportUser":null,"ReportUserId":null,"AuditTime":null,"AuditUser":null,"AuditUserId":null,"FinalAuditTime":null,"FinalAuditUser":null,"FinalAuditUserId":null,"ReportPrintTime":null,"ReportPrintUser":null,"FilmPrintTime":null,"FilmPrintUser":null,"ReportConclusion":null,"ReportDiscription":null,"ReportRemark":null,"IsNormal":null,"IsNormalDetail":null,"DistributedTime":null,"DistributedStatus":null,"canCelMes":null}
